/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary: #ff2e4d;
    --primary-dark: #e61e3d;
    --primary-light: #ff5470;
    --secondary: #ffd700;
    --accent: #00d5ff;
    
    /* Neutrals */
    --background: #0a0a0a;
    --surface: #111111;
    --surface-light: #1a1a1a;
    --surface-lighter: #242424;
    --border: #2a2a2a;
    --border-light: #3a3a3a;
    
    /* Text */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-tertiary: #808080;
    
    /* Status */
    --success: #22c55e;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Effects */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.4);
    --shadow-md: 0 4px 16px rgba(0,0,0,0.5);
    --shadow-lg: 0 10px 40px rgba(0,0,0,0.6);
    --glow: 0 0 20px rgba(255, 46, 77, 0.3);
    
    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-base: 250ms ease;
    --transition-slow: 350ms ease;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: var(--background);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* App Container */
.app-container {
    display: flex;
    height: 100vh;
    position: relative;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: var(--surface);
    border-right: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    transition: transform var(--transition-base);
    z-index: 100;
}

/* Persona Switcher */
.persona-switcher {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
    background: var(--surface-light);
}

.persona-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.persona-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.btn-icon:hover {
    background: var(--surface-lighter);
    color: var(--text-primary);
}

.persona-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 200px;
    overflow-y: auto;
}

.persona-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.persona-card:hover {
    background: var(--surface-lighter);
    border-color: var(--border-light);
}

.persona-card.active {
    background: rgba(255, 46, 77, 0.1);
    border-color: var(--primary);
}

.persona-avatar {
    width: 48px;
    height: 48px;
    background: var(--surface-lighter);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.persona-info h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.persona-info p {
    font-size: 12px;
    color: var(--text-tertiary);
}

/* Navigation Menu */
.nav-menu {
    flex: 1;
    padding: var(--spacing-md) 0;
    overflow-y: auto;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-item:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.05);
}

.nav-item.active {
    color: var(--primary);
    background: rgba(255, 46, 77, 0.1);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 24px;
    background: var(--primary);
    border-radius: 0 2px 2px 0;
}

.nav-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

/* Main Content */
.main-content {
    flex: 1;
    background: var(--background);
    overflow-y: auto;
    position: relative;
}

/* Page Container */
.page-container {
    min-height: 100vh;
    animation: fadeIn var(--transition-base);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header Bar */
.header-bar {
    position: sticky;
    top: 0;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border);
    padding: var(--spacing-lg) var(--spacing-xl);
    z-index: 50;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-xl);
}

.page-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
}

/* Content Section */
.content-section {
    padding: var(--spacing-xl);
}

/* Card Components */
.card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: all var(--transition-base);
}

.card:hover {
    border-color: var(--border-light);
    box-shadow: var(--shadow-md);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

/* Button Components */
.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    box-shadow: var(--glow);
}

.btn-secondary {
    background: var(--surface-lighter);
    color: var(--text-primary);
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    background: var(--surface-light);
    border-color: var(--border-light);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
}

.btn-ghost:hover {
    background: var(--surface-lighter);
    color: var(--text-primary);
}

/* Form Elements */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.form-input,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 14px;
    transition: all var(--transition-fast);
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary);
    background: var(--surface-lighter);
    box-shadow: 0 0 0 3px rgba(255, 46, 77, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

/* Grid Layout */
.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
}

/* Toast Notifications */
#toastContainer {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.toast {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    min-width: 300px;
    box-shadow: var(--shadow-lg);
    animation: slideIn var(--transition-base);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast-success {
    border-color: var(--success);
    background: rgba(34, 197, 94, 0.1);
}

.toast-error {
    border-color: var(--error);
    background: rgba(239, 68, 68, 0.1);
}

.toast-warning {
    border-color: var(--warning);
    background: rgba(245, 158, 11, 0.1);
}

.toast-info {
    border-color: var(--info);
    background: rgba(59, 130, 246, 0.1);
}

/* Modal */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn var(--transition-fast);
}

.modal {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalSlideIn var(--transition-base);
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.modal-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--surface-lighter);
    color: var(--text-primary);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 56px;
    height: 56px;
    background: var(--primary);
    border: none;
    border-radius: var(--radius-full);
    color: white;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    z-index: 200;
    transition: all var(--transition-fast);
}

.mobile-menu-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--glow), var(--shadow-lg);
}

/* Loading States */
.skeleton {
    background: linear-gradient(90deg, var(--surface) 25%, var(--surface-light) 50%, var(--surface) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s ease-in-out infinite;
    border-radius: var(--radius-md);
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border);
    border-top-color: var(--primary);
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -280px;
        height: 100%;
        box-shadow: var(--shadow-lg);
    }
    
    .sidebar.active {
        transform: translateX(280px);
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
    
    .content-section {
        padding: var(--spacing-lg);
    }
    
    #toastContainer {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
        left: var(--spacing-lg);
    }
    
    .toast {
        width: 100%;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.flex {
    display: flex;
}

.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.hidden {
    display: none !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface);
}

::-webkit-scrollbar-thumb {
    background: var(--surface-lighter);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-light);
}

/* Sources Page Styles */
.source-category {
    background: var(--surface-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.source-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.source-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.source-item:hover {
    background: var(--surface-lighter);
    border-color: var(--border-light);
}

.source-item input[type="checkbox"] {
    margin: 0;
}

.source-info {
    flex: 1;
}

.source-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.source-desc {
    font-size: 12px;
    color: var(--text-tertiary);
}

.source-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    background: var(--surface-lighter);
    color: var(--text-secondary);
}

.custom-sources {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.custom-source-item {
    padding: var(--spacing-md);
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
}

/* Camera Roll / Media Grid Styles */
.media-card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.media-card:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-md);
}

.media-card.used {
    border-color: var(--success);
    background: rgba(34, 197, 94, 0.05);
}

.media-card.unused {
    border-color: var(--border);
}

.media-card .media-thumbnail {
    position: relative;
    aspect-ratio: 1;
    background: var(--surface-light);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.media-card .media-icon {
    font-size: 32px;
    opacity: 0.6;
}

.media-overlay {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 12px;
    border-radius: var(--radius-sm);
}

.media-info {
    padding: var(--spacing-md);
}

.media-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.media-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 8px;
}

.tag {
    padding: 2px 6px;
    background: var(--surface-lighter);
    color: var(--text-secondary);
    font-size: 11px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border);
}

.media-category {
    font-size: 12px;
    color: var(--text-tertiary);
}

/* Enhanced Media Status Indicators */
.media-status {
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.media-card.used .media-status {
    background: var(--success);
    color: white;
}

.media-card.unused .media-status {
    background: var(--warning);
    color: white;
}

/* Generated Content Page Styles */
.generation-steps {
    display: flex;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-lg);
}

.step-item {
    flex: 1;
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--surface-light);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    transition: all var(--transition-base);
}

.step-item.active {
    border-color: var(--primary);
    background: rgba(255, 46, 77, 0.05);
}

.step-number {
    width: 32px;
    height: 32px;
    background: var(--surface-lighter);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: var(--text-secondary);
    flex-shrink: 0;
}

.step-item.active .step-number {
    background: var(--primary);
    color: white;
}

.step-content h4,
.step-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.step-content p,
.step-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

.generated-content-card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: all var(--transition-base);
}

.generated-content-card:hover {
    border-color: var(--border-light);
    box-shadow: var(--shadow-md);
}

.content-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.content-meta {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-md);
}

/* Progress Stats Styles */
.progress-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    text-align: center;
}

.stat-item {
    padding: var(--spacing-md);
    background: var(--surface-light);
    border-radius: var(--radius-md);
}

.stat-label {
    font-size: 12px;
    color: var(--text-tertiary);
    margin-bottom: 4px;
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary);
}

/* Dashboard Generation Panel */
.generation-panel {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.generation-input-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.generation-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 16px;
    transition: all var(--transition-fast);
}

.generation-input:focus {
    outline: none;
    border-color: var(--primary);
    background: var(--surface-lighter);
    box-shadow: 0 0 0 3px rgba(255, 46, 77, 0.1);
}

.generation-input::placeholder {
    color: var(--text-tertiary);
}

.generation-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
}

.generation-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 14px;
    color: var(--text-secondary);
    cursor: pointer;
}

.count-select {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 14px;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.count-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(255, 46, 77, 0.1);
}

/* Ideas Grid */
.ideas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.idea-card {
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.idea-card:hover {
    border-color: var(--primary);
    background: var(--surface-lighter);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.idea-card.selected {
    border-color: var(--primary);
    background: rgba(255, 46, 77, 0.1);
    box-shadow: 0 0 0 2px rgba(255, 46, 77, 0.2);
}

.idea-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.idea-card:hover::before,
.idea-card.selected::before {
    opacity: 1;
}

.idea-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
}

.idea-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
}

.idea-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.idea-tag {
    padding: 2px 8px;
    background: var(--surface-lighter);
    color: var(--text-tertiary);
    font-size: 12px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border);
}

.idea-card.selected .idea-tag {
    background: rgba(255, 46, 77, 0.2);
    border-color: var(--primary);
    color: var(--primary);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all var(--transition-fast);
}

.stat-card:hover {
    border-color: var(--border-light);
    box-shadow: var(--shadow-sm);
}

.stat-card .stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.stat-card .stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Photo Usage Card */
.photo-usage-card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.photo-usage-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.photo-usage-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.photo-stat {
    text-align: center;
}

.photo-stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
}

.photo-stat-label {
    font-size: 12px;
    color: var(--text-tertiary);
}

.photo-usage-bar {
    height: 8px;
    background: var(--surface-lighter);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.photo-usage-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    border-radius: var(--radius-sm);
    transition: width var(--transition-base);
}

/* Xiaohongshu-inspired enhancements */
.xiaohongshu-accent {
    color: var(--primary);
    font-weight: 600;
}

.xiaohongshu-gradient {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.card-xiaohongshu {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
}

.card-xiaohongshu::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light), var(--secondary));
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.card-xiaohongshu:hover::before {
    opacity: 1;
}

.card-xiaohongshu:hover {
    border-color: var(--border-light);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* Generate Button Enhancement */
.generate-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 16px;
    font-weight: 600;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    min-height: 44px;
    white-space: nowrap;
    flex-shrink: 0;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--glow), var(--shadow-md);
}

.generate-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Progress Bar for Photo Usage */
.progress-bar {
    width: 100%;
    height: 12px;
    background: var(--surface-lighter);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    border-radius: var(--radius-sm);
    transition: width var(--transition-base);
}

/* Recent Content List */
.recent-content-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.recent-item {
    padding: var(--spacing-md);
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.recent-item:hover {
    background: var(--surface-lighter);
    border-color: var(--border-light);
}

.recent-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.recent-time {
    font-size: 12px;
    color: var(--text-tertiary);
}

.badge {
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 11px;
    font-weight: 500;
}

.badge-success {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success);
}

.badge-secondary {
    background: var(--surface-lighter);
    color: var(--text-secondary);
}

/* Persona Detail Card */
.persona-detail-card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.persona-main-layout {
    display: grid;
    grid-template-columns: 240px 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.persona-left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.persona-card-compact {
    background: var(--surface-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    border: 1px solid var(--border);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all var(--transition-fast);
}

.persona-card-compact:hover {
    border-color: var(--primary);
    box-shadow: 0 4px 16px rgba(255, 46, 77, 0.1);
}

.persona-avatar-compact {
    width: 60px;
    height: 60px;
    background: var(--surface);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    flex-shrink: 0;
    border: 2px solid var(--border);
}

.persona-info-compact {
    flex: 1;
}

.persona-name-compact {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.persona-tags {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.persona-tag {
    padding: 2px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border);
}

.gender-tag {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info);
    border-color: var(--info);
}

.mbti-tag {
    background: rgba(139, 69, 19, 0.1);
    color: #8B4513;
    border-color: #8B4513;
}

.persona-stats {
    display: flex;
    gap: var(--spacing-sm);
    background: var(--surface-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    border: 1px solid var(--border);
}

.stat-compact {
    flex: 1;
    text-align: center;
}

.stat-number {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 2px;
}

.stat-label-compact {
    font-size: 11px;
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.persona-right {
    min-height: 200px;
}

.persona-introduction {
    background: linear-gradient(135deg, var(--surface-light), var(--surface));
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 1px solid var(--border);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.persona-introduction::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
}

.introduction-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border);
    position: relative;
}

.introduction-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--primary);
}

.introduction-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.intro-section {
    padding: var(--spacing-lg);
    background: var(--surface);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    transition: all var(--transition-fast);
    position: relative;
    animation: fadeInStaggered 0.6s ease-out;
}

.intro-section:nth-child(1) { animation-delay: 0.1s; }
.intro-section:nth-child(2) { animation-delay: 0.2s; }
.intro-section:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInStaggered {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.intro-section:hover {
    border-color: var(--primary);
    box-shadow: 0 4px 12px rgba(255, 46, 77, 0.1);
    transform: translateY(-2px);
}

.intro-subtitle {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border);
}

.intro-subtitle::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary);
    flex-shrink: 0;
}

.intro-text {
    font-size: 15px;
    line-height: 1.7;
    color: var(--text-secondary);
    margin: 0;
    text-align: justify;
}

.topics-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.topic-tag {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface-lighter);
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 500;
    border-radius: var(--radius-full);
    border: 1px solid var(--border);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.topic-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 46, 77, 0.1), transparent);
    transition: left var(--transition-base);
}

.topic-tag:hover::before {
    left: 100%;
}

.topic-tag:hover {
    background: rgba(255, 46, 77, 0.1);
    border-color: var(--primary);
    color: var(--primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 46, 77, 0.2);
}

/* Photo Management Button */
.btn-photo-manage {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
    font-weight: 600;
    border: none;
    box-shadow: 0 4px 12px rgba(255, 46, 77, 0.3);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    width: auto;
    min-width: 120px;
    justify-content: center;
}

.btn-photo-manage::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-base);
}

.btn-photo-manage:hover::before {
    left: 100%;
}

.btn-photo-manage:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 46, 77, 0.4);
}

.btn-photo-manage:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(255, 46, 77, 0.3);
}

.btn-photo-manage svg {
    margin-right: var(--spacing-xs);
}

/* Compact Generate Panel */
.generate-panel-compact {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    flex: 1;
    max-width: 800px;
    min-width: 500px;
}

.generate-input-compact {
    flex: 1;
    padding: var(--spacing-lg);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 16px;
    transition: all var(--transition-fast);
    min-height: 48px;
}

.generate-input-compact:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(255, 46, 77, 0.1);
}

.generate-input-compact::placeholder {
    color: var(--text-tertiary);
}

.generate-btn-compact {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
    flex-shrink: 0;
    min-height: 48px;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    box-shadow: 0 4px 12px rgba(255, 46, 77, 0.2);
    transition: all var(--transition-fast);
}

.generate-btn-compact:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 46, 77, 0.3);
}

/* Home-style Ideas Grid */
.ideas-grid-home {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
}

.idea-card-home {
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.idea-card-home:hover {
    border-color: var(--primary);
    box-shadow: 0 6px 20px rgba(255, 46, 77, 0.15);
    transform: translateY(-3px);
}

.idea-card-home::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.idea-card-home:hover::before {
    opacity: 1;
}

.idea-header-home {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.idea-title-home {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.4;
    margin: 0;
}

.heat-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
}

.heat-badge.hot {
    background: rgba(255, 46, 77, 0.1);
    color: var(--primary);
    border: 1px solid var(--primary);
}

.heat-badge.warm {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    border: 1px solid var(--warning);
}

.heat-badge.cool {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info);
    border: 1px solid var(--info);
}

.idea-description-home {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.idea-source-home {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-tertiary);
    font-size: 12px;
}

.source-icon {
    opacity: 0.7;
}

/* Trends Section */
.trends-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-md);
}

.trends-column {
    background: var(--surface-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border);
}

.trends-subtitle {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.trends-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* Responsive adjustments for dashboard */
@media (max-width: 768px) {
    .ideas-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .generation-options {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }
    
    .generation-option {
        justify-content: center;
    }
    
    .generate-btn {
        width: 100%;
    }
    
    .photo-usage-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .persona-main-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .persona-left {
        gap: var(--spacing-md);
    }
    
    .persona-card-compact {
        padding: var(--spacing-md);
    }
    
    .persona-avatar-compact {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }
    
    .persona-name-compact {
        font-size: 18px;
    }
    
    .persona-stats {
        padding: var(--spacing-sm);
        gap: var(--spacing-xs);
    }
    
    .stat-number {
        font-size: 16px;
    }
    
    .stat-label-compact {
        font-size: 10px;
    }
    
    .persona-detail-card {
        padding: var(--spacing-lg);
    }
    
    .persona-introduction {
        padding: var(--spacing-lg);
    }
    
    .introduction-title {
        font-size: 18px;
        margin-bottom: var(--spacing-lg);
    }
    
    .introduction-content {
        gap: var(--spacing-lg);
    }
    
    .intro-section {
        padding: var(--spacing-md);
    }
    
    .intro-section:hover {
        transform: none;
    }
    
    .intro-subtitle {
        font-size: 15px;
    }
    
    .intro-text {
        font-size: 14px;
        line-height: 1.6;
        text-align: left;
    }
    
    .topic-tag {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 12px;
    }
    
    .topics-grid {
        gap: var(--spacing-xs);
    }
    
    .generate-panel-compact {
        flex-direction: column;
        gap: var(--spacing-md);
        max-width: none;
        min-width: auto;
        padding: var(--spacing-md);
    }
    
    .generate-input-compact {
        width: 100%;
        padding: var(--spacing-md);
        font-size: 14px;
        min-height: 44px;
    }
    
    .generate-btn-compact {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 14px;
        min-height: 44px;
    }
    
    .ideas-grid-home {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .trends-section {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .trends-column {
        padding: var(--spacing-md);
    }
    
    .idea-card-home {
        padding: var(--spacing-md);
    }
    
    .idea-card-home:hover {
        transform: none;
    }
    
    .generation-steps {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .step-item {
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
        text-align: center;
    }
    
    .step-number {
        align-self: center;
        margin-bottom: var(--spacing-sm);
    }
}

/* Settings Page Styles */
.setting-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    transition: all var(--transition-fast);
}

.setting-item:hover {
    background: var(--surface-lighter);
    border-color: var(--border-light);
}

.setting-item:last-child {
    margin-bottom: 0;
}

/* Switch Component */
.switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
    flex-shrink: 0;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--surface-lighter);
    transition: var(--transition-base);
    border-radius: var(--radius-full);
    border: 1px solid var(--border);
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 3px;
    bottom: 3px;
    background-color: var(--text-secondary);
    transition: var(--transition-base);
    border-radius: var(--radius-full);
}

input:checked + .slider {
    background-color: var(--primary);
    border-color: var(--primary);
}

input:checked + .slider:before {
    transform: translateX(24px);
    background-color: white;
}

.slider:hover {
    box-shadow: 0 0 8px rgba(255, 46, 77, 0.2);
}

/* Checkbox styling */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface-lighter);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 14px;
}

.checkbox-label:hover {
    background: var(--surface-light);
    border-color: var(--border-light);
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"]:checked {
    accent-color: var(--primary);
}

/* Knowledge Base Upload Styles */
.knowledge-upload-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.upload-area {
    border: 2px dashed var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-base);
    background: var(--surface-light);
}

.upload-area:hover {
    border-color: var(--primary);
    background: rgba(255, 46, 77, 0.03);
}

.upload-area.dragover {
    border-color: var(--primary);
    background: rgba(255, 46, 77, 0.05);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.6;
}

.upload-text {
    color: var(--text-secondary);
}

.uploaded-files {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.uploaded-file-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.uploaded-file-item:hover {
    background: var(--surface-lighter);
    border-color: var(--border-light);
}

.file-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 12px;
    color: var(--text-tertiary);
}

.file-separator {
    color: var(--text-tertiary);
}

.file-size {
    color: var(--text-secondary);
}

.file-time {
    color: var(--text-secondary);
}

.file-status {
    font-weight: 500;
}

.file-status.processed {
    color: var(--success);
}

.file-status.processing {
    color: var(--warning);
}

.file-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-shrink: 0;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 12px;
    min-height: 32px;
    width: 32px;
    justify-content: center;
}

.btn-sm svg {
    width: 14px;
    height: 14px;
}

/* Drag and Drop Enhancement */
.upload-area.dragover::after {
    content: '释放文件以上传';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    font-weight: 600;
    color: var(--primary);
    pointer-events: none;
}

/* File Type Icons */
.file-icon-pdf { color: #e74c3c; }
.file-icon-word { color: #2980b9; }
.file-icon-txt { color: #95a5a6; }
.file-icon-markdown { color: #f39c12; }
.file-icon-json { color: #27ae60; }

/* Responsive Design for Upload */
@media (max-width: 768px) {
    .upload-area {
        padding: var(--spacing-lg);
    }
    
    .upload-icon {
        font-size: 32px;
    }
    
    .uploaded-file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .file-actions {
        align-self: flex-end;
    }
    
    .file-name {
        white-space: normal;
        overflow: visible;
        text-overflow: initial;
    }
}