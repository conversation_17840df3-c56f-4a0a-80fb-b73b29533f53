<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xiaohongshu AI Content Assistant</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <!-- Persona Switcher -->
            <div class="persona-switcher">
                <div class="persona-header">
                    <h3>Writing Personas</h3>
                    <button class="btn-icon" id="addPersonaBtn">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"/>
                        </svg>
                    </button>
                </div>
                <div class="persona-list" id="personaList">
                    <!-- Empty State for Personas -->
                    <div class="empty-persona-state">
                        <div class="empty-persona-visual">
                            <div class="empty-persona-icon">
                                <svg width="32" height="32" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"/>
                                </svg>
                            </div>
                            <div class="empty-persona-text">
                                <h4>No Personas Yet</h4>
                                <p>Create your first writing persona to get started</p>
                            </div>
                        </div>
                        <button class="btn btn-primary btn-create-persona" onclick="createFirstPersona()">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"/>
                            </svg>
                            Create Persona
                        </button>
                    </div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="nav-menu">
                <a href="#" class="nav-item active" data-page="dashboard">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                    </svg>
                    Dashboard
                </a>
                <a href="#" class="nav-item" data-page="content">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"/>
                    </svg>
                    Content Studio
                </a>
                <a href="#" class="nav-item" data-page="gallery">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"/>
                    </svg>
                    Camera Roll
                </a>
                <a href="#" class="nav-item" data-page="sources">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                        <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"/>
                    </svg>
                    Content Sources
                </a>
                <a href="#" class="nav-item" data-page="settings">
                    <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"/>
                    </svg>
                    Settings
                </a>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Empty Dashboard Content -->
            <div class="page-container">
                <!-- Header Bar -->
                <div class="header-bar">
                    <div class="header-content">
                        <h1 class="page-title">Welcome to Your Content Studio</h1>
                        <!-- Empty Generation Panel -->
                        <div class="generation-panel-empty">
                            <div class="empty-input-section">
                                <input 
                                    type="text" 
                                    class="generation-input-disabled" 
                                    placeholder="Create a persona first to start generating content..."
                                    disabled
                                />
                                <button class="btn btn-primary generate-btn-disabled" disabled>
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                    </svg>
                                    Generate Content
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="content-section">
                    <!-- Welcome Banner -->
                    <div class="welcome-banner" id="welcomeBanner">
                        <button class="banner-close" onclick="closeBanner()" aria-label="Close banner">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"/>
                            </svg>
                        </button>
                        
                        <div class="banner-visual">
                            <div class="banner-illustration">
                                <!-- Outer rotating ring -->
                                <div class="illustration-ring ring-outer">
                                    <div class="ring-particle particle-1">
                                        <div class="particle-icon">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                                <polyline points="21 15 16 10 5 21"></polyline>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ring-particle particle-2">
                                        <div class="particle-icon">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M12 19l7-7 3 3-7 7-3-3z"></path>
                                                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path>
                                                <path d="M2 2l7.586 7.586"></path>
                                                <circle cx="11" cy="11" r="2"></circle>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ring-particle particle-3">
                                        <div class="particle-icon">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <circle cx="12" cy="12" r="6"></circle>
                                                <circle cx="12" cy="12" r="2"></circle>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Middle rotating ring -->
                                <div class="illustration-ring ring-middle">
                                    <div class="ring-glow"></div>
                                    <div class="ring-dots">
                                        <span class="dot"></span>
                                        <span class="dot"></span>
                                        <span class="dot"></span>
                                        <span class="dot"></span>
                                    </div>
                                </div>
                                
                                <!-- Inner content -->
                                <div class="illustration-core">
                                    <div class="core-background"></div>
                                    <div class="core-icon">
                                        <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                            <circle cx="8.5" cy="7" r="4"></circle>
                                            <path d="M20 8v6"></path>
                                            <path d="M23 11h-6"></path>
                                        </svg>
                                    </div>
                                    <div class="core-sparkles">
                                        <div class="sparkle-star star-1">
                                            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                                                <path d="M6 0L7.5 4.5L12 6L7.5 7.5L6 12L4.5 7.5L0 6L4.5 4.5L6 0Z"/>
                                            </svg>
                                        </div>
                                        <div class="sparkle-star star-2">
                                            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                                                <path d="M6 0L7.5 4.5L12 6L7.5 7.5L6 12L4.5 7.5L0 6L4.5 4.5L6 0Z"/>
                                            </svg>
                                        </div>
                                        <div class="sparkle-star star-3">
                                            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                                                <path d="M6 0L7.5 4.5L12 6L7.5 7.5L6 12L4.5 7.5L0 6L4.5 4.5L6 0Z"/>
                                            </svg>
                                        </div>
                                        <div class="sparkle-star star-4">
                                            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                                                <path d="M6 0L7.5 4.5L12 6L7.5 7.5L6 12L4.5 7.5L0 6L4.5 4.5L6 0Z"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Floating elements -->
                                <div class="floating-elements">
                                    <div class="float-bubble bubble-1">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect x="3" y="11" width="18" height="10" rx="2" ry="2"></rect>
                                            <path d="M7 11V7a5 5 0 0110 0v4"></path>
                                        </svg>
                                        <span>AI</span>
                                    </div>
                                    <div class="float-bubble bubble-2">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <line x1="12" y1="1" x2="12" y2="23"></line>
                                            <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                                        </svg>
                                        <span>Fast</span>
                                    </div>
                                    <div class="float-bubble bubble-3">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                        </svg>
                                        <span>Pro</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="banner-content">
                            <div class="banner-badge">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                Welcome to Your AI Content Studio
                            </div>
                            
                            <h1 class="banner-title">
                                Create Your First 
                                <span class="title-highlight">Writing Persona</span>
                            </h1>
                            
                            <p class="banner-description">
                                Transform any photo into captivating social media posts with AI that understands your unique voice and style. Get started in just 2 minutes!
                            </p>
                            
                            <div class="banner-features">
                                <div class="feature-item">
                                    <div class="feature-icon">🚀</div>
                                    <span>Generate posts in seconds</span>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon">🎯</div>
                                    <span>Smart photo analysis</span>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon">🔥</div>
                                    <span>Trending topic integration</span>
                                </div>
                            </div>
                            
                            <div class="banner-actions">
                                <button class="btn btn-primary banner-cta" onclick="createFirstPersona()">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"/>
                                    </svg>
                                    Create Your First Persona
                                </button>
                                <button class="btn btn-ghost banner-secondary" onclick="showDemo()">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"/>
                                    </svg>
                                    Watch Demo
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Empty State (shows when banner is closed) -->
                    <div class="empty-dashboard-state" id="emptyState" style="display: none;">
                        <div class="empty-state-container">
                            <div class="empty-state-visual">
                                <div class="empty-icon-wrapper">
                                    <svg width="80" height="80" fill="currentColor" viewBox="0 0 20 20" class="empty-main-icon">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"/>
                                    </svg>
                                    <div class="empty-sparkles">
                                        <div class="sparkle sparkle-1">✨</div>
                                        <div class="sparkle sparkle-2">⭐</div>
                                        <div class="sparkle sparkle-3">💫</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="empty-state-content">
                                <h2 class="empty-state-title">Ready to Create Amazing Content?</h2>
                                <p class="empty-state-description">
                                    Create your first writing persona to unlock the power of AI-generated social media posts. 
                                    Each persona will have its own unique voice and style.
                                </p>
                                
                                <button class="btn btn-primary empty-state-cta" onclick="createFirstPersona()">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"/>
                                    </svg>
                                    Create Your First Persona
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
        </svg>
    </button>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <!-- Modal Container -->
    <div id="modalContainer"></div>

    <style>
        /* Empty State Specific Styles */
        
        /* Empty Persona State */
        .empty-persona-state {
            padding: var(--spacing-lg);
            text-align: center;
            background: var(--surface-light);
            border: 2px dashed var(--border);
            border-radius: var(--radius-lg);
            transition: all var(--transition-fast);
        }

        .empty-persona-state:hover {
            border-color: var(--primary);
            background: rgba(255, 46, 77, 0.03);
        }

        .empty-persona-visual {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .empty-persona-icon {
            width: 48px;
            height: 48px;
            background: var(--surface-lighter);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-tertiary);
        }

        .empty-persona-text h4 {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .empty-persona-text p {
            font-size: 12px;
            color: var(--text-tertiary);
            margin: 0;
        }

        .btn-create-persona {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 12px;
        }

        /* Empty Generation Panel */
        .generation-panel-empty {
            background: var(--surface-light);
            border: 1px solid var(--border);
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            opacity: 0.6;
        }

        .empty-input-section {
            display: flex;
            gap: var(--spacing-md);
            align-items: center;
        }

        .generation-input-disabled {
            flex: 1;
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            color: var(--text-tertiary);
            font-size: 14px;
            cursor: not-allowed;
        }

        .generate-btn-disabled {
            opacity: 0.5;
            cursor: not-allowed;
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: 14px;
        }

        /* Welcome Banner */
        .welcome-banner {
            background: linear-gradient(135deg, 
                rgba(255, 46, 77, 0.05) 0%, 
                var(--surface-light) 25%, 
                var(--surface) 100%);
            border: 2px solid var(--primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-xl);
            position: relative;
            overflow: hidden;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: var(--spacing-2xl);
            align-items: center;
            min-height: 300px;
            box-shadow: var(--shadow-lg);
        }

        .welcome-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--primary-light), var(--secondary));
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .banner-close {
            position: absolute;
            top: var(--spacing-lg);
            right: var(--spacing-lg);
            width: 32px;
            height: 32px;
            background: var(--surface-lighter);
            border: 1px solid var(--border);
            border-radius: var(--radius-full);
            color: var(--text-secondary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            z-index: 10;
        }

        .banner-close:hover {
            background: var(--surface-light);
            color: var(--text-primary);
            transform: scale(1.1);
        }

        .banner-visual {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .banner-illustration {
            position: relative;
            width: 240px;
            height: 240px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Rotating Rings */
        .illustration-ring {
            position: absolute;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ring-outer {
            width: 220px;
            height: 220px;
            animation: rotate 20s linear infinite;
        }

        .ring-middle {
            width: 160px;
            height: 160px;
            border: 2px solid rgba(255, 46, 77, 0.2);
            animation: rotate-reverse 15s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes rotate-reverse {
            from { transform: rotate(360deg); }
            to { transform: rotate(0deg); }
        }

        /* Ring Particles */
        .ring-particle {
            position: absolute;
            width: 50px;
            height: 50px;
            background: var(--surface-light);
            border: 2px solid var(--primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(255, 46, 77, 0.3);
        }

        .particle-1 {
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            animation: float-particle 3s ease-in-out infinite;
        }

        .particle-2 {
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            animation: float-particle 3s ease-in-out infinite;
            animation-delay: 1s;
        }

        .particle-3 {
            top: 50%;
            right: -25px;
            transform: translateY(-50%);
            animation: float-particle 3s ease-in-out infinite;
            animation-delay: 2s;
        }

        @keyframes float-particle {
            0%, 100% { 
                transform: translateX(-50%) scale(1);
                filter: brightness(1);
            }
            50% { 
                transform: translateX(-50%) scale(1.1);
                filter: brightness(1.2);
            }
        }

        .particle-icon {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            animation: bounce 2s ease-in-out infinite;
        }

        .particle-icon svg {
            width: 24px;
            height: 24px;
        }

        /* Ring Glow */
        .ring-glow {
            position: absolute;
            inset: -20px;
            background: radial-gradient(
                circle at center,
                rgba(255, 46, 77, 0.2) 0%,
                rgba(255, 46, 77, 0.1) 40%,
                transparent 70%
            );
            border-radius: 50%;
            animation: pulse-glow 3s ease-in-out infinite;
        }

        @keyframes pulse-glow {
            0%, 100% { opacity: 0.5; transform: scale(0.9); }
            50% { opacity: 1; transform: scale(1.1); }
        }

        /* Ring Dots */
        .ring-dots {
            position: absolute;
            inset: 0;
        }

        .ring-dots .dot {
            position: absolute;
            width: 6px;
            height: 6px;
            background: var(--primary);
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(255, 46, 77, 0.5);
        }

        .ring-dots .dot:nth-child(1) { top: 0; left: 50%; transform: translateX(-50%); }
        .ring-dots .dot:nth-child(2) { right: 0; top: 50%; transform: translateY(-50%); }
        .ring-dots .dot:nth-child(3) { bottom: 0; left: 50%; transform: translateX(-50%); }
        .ring-dots .dot:nth-child(4) { left: 0; top: 50%; transform: translateY(-50%); }

        /* Core Illustration */
        .illustration-core {
            position: absolute;
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .core-background {
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            border-radius: 50%;
            box-shadow: 
                0 10px 30px rgba(255, 46, 77, 0.3),
                inset 0 -5px 10px rgba(0, 0, 0, 0.2);
            animation: pulse 3s ease-in-out infinite;
        }

        .core-icon {
            position: relative;
            z-index: 10;
            color: white;
            animation: float 4s ease-in-out infinite;
        }

        /* Core Sparkles */
        .core-sparkles {
            position: absolute;
            inset: -20px;
            pointer-events: none;
        }

        .sparkle-star {
            position: absolute;
            width: 12px;
            height: 12px;
            color: var(--primary);
            animation: sparkle-rotate 4s linear infinite;
        }

        .sparkle-star svg {
            width: 100%;
            height: 100%;
        }

        .star-1 { top: 0; left: 50%; transform: translateX(-50%); }
        .star-2 { right: 0; top: 50%; transform: translateY(-50%); animation-delay: 1s; }
        .star-3 { bottom: 0; left: 50%; transform: translateX(-50%); animation-delay: 2s; }
        .star-4 { left: 0; top: 50%; transform: translateY(-50%); animation-delay: 3s; }

        @keyframes sparkle-rotate {
            0%, 100% { 
                opacity: 0;
                transform: scale(0.5) rotate(0deg);
            }
            50% { 
                opacity: 1;
                transform: scale(1.2) rotate(180deg);
            }
        }

        /* Floating Bubbles */
        .floating-elements {
            position: absolute;
            inset: -40px;
            pointer-events: none;
        }

        .float-bubble {
            position: absolute;
            padding: 8px 14px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid var(--primary);
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            color: var(--primary);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            animation: float-bubble 6s ease-in-out infinite;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .float-bubble svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .bubble-1 {
            top: 20px;
            left: -20px;
            animation-delay: 0s;
        }

        .bubble-2 {
            top: 20px;
            right: -20px;
            animation-delay: 2s;
        }

        .bubble-3 {
            bottom: 40px;
            right: 10px;
            animation-delay: 4s;
        }

        @keyframes float-bubble {
            0%, 100% { 
                transform: translateY(0) scale(1);
                opacity: 0.8;
            }
            25% {
                transform: translateY(-10px) scale(1.05);
                opacity: 1;
            }
            50% { 
                transform: translateY(5px) scale(0.95);
                opacity: 0.9;
            }
            75% {
                transform: translateY(-5px) scale(1.02);
                opacity: 1;
            }
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .banner-content {
            padding-left: var(--spacing-lg);
        }

        .banner-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-md);
            background: rgba(255, 46, 77, 0.1);
            border: 1px solid var(--primary);
            color: var(--primary);
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            margin-bottom: var(--spacing-lg);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 5px rgba(255, 46, 77, 0.2); }
            to { box-shadow: 0 0 15px rgba(255, 46, 77, 0.4); }
        }

        .banner-title {
            font-size: 36px;
            font-weight: 800;
            color: var(--text-primary);
            line-height: 1.2;
            margin-bottom: var(--spacing-lg);
        }

        .title-highlight {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .banner-description {
            font-size: 16px;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-xl);
        }

        .banner-features {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-2xl);
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 14px;
            color: var(--text-secondary);
        }

        .feature-icon {
            font-size: 20px;
        }

        .banner-actions {
            display: flex;
            gap: var(--spacing-lg);
        }

        .banner-cta {
            padding: var(--spacing-lg) var(--spacing-2xl);
            font-size: 16px;
            font-weight: 600;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            box-shadow: var(--shadow-md);
            transition: all var(--transition-fast);
        }

        .banner-cta:hover {
            transform: translateY(-2px);
            box-shadow: var(--glow), var(--shadow-lg);
        }

        .banner-secondary {
            padding: var(--spacing-lg) var(--spacing-xl);
            font-size: 16px;
            font-weight: 600;
        }

        /* Potential Showcase Grid */
        .potential-showcase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .potential-card {
            background: var(--surface-light);
            border: 1px solid var(--border);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
        }

        .potential-card.highlight {
            border-color: var(--primary);
            background: linear-gradient(135deg, rgba(255, 46, 77, 0.05), var(--surface-light));
        }

        .potential-card.highlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary), var(--primary-light));
        }

        .potential-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary);
        }

        .potential-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-lg);
        }

        .potential-icon {
            font-size: 32px;
        }

        .potential-badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            color: var(--text-secondary);
        }

        .potential-badge.trending-badge {
            background: rgba(255, 46, 77, 0.1);
            border-color: var(--primary);
            color: var(--primary);
            animation: pulse 2s infinite;
        }

        .potential-content h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .potential-content p {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-lg);
        }

        .potential-action {
            margin-top: var(--spacing-lg);
        }

        /* Potential Card Demos */
        .potential-demo {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
        }

        .demo-photo {
            width: 40px;
            height: 40px;
            background: var(--surface-lighter);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .demo-arrow {
            color: var(--primary);
            font-weight: 700;
        }

        .demo-result {
            flex: 1;
        }

        .demo-line {
            height: 6px;
            background: var(--surface-lighter);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-xs);
        }

        .demo-line.short {
            width: 60%;
        }

        .demo-tags {
            display: flex;
            gap: var(--spacing-xs);
            margin-top: var(--spacing-sm);
        }

        .demo-tags span {
            padding: 2px 6px;
            background: rgba(255, 46, 77, 0.1);
            color: var(--primary);
            font-size: 10px;
            border-radius: var(--radius-sm);
        }

        /* Trending Preview */
        .trending-preview {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .trending-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            background: var(--surface);
            border-radius: var(--radius-md);
            font-size: 12px;
            color: var(--text-secondary);
        }

        .trend-dot {
            width: 8px;
            height: 8px;
            border-radius: var(--radius-full);
            flex-shrink: 0;
        }

        .trend-dot.hot {
            background: var(--primary);
            animation: pulse 1.5s infinite;
        }

        .trend-dot.warm {
            background: var(--warning);
        }

        .trend-dot.cool {
            background: var(--info);
        }

        /* Persona Preview */
        .persona-preview {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }

        .mini-persona-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm);
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-md);
            font-size: 11px;
            color: var(--text-secondary);
            transition: all var(--transition-fast);
        }

        .mini-persona-card:hover {
            border-color: var(--primary);
            background: rgba(255, 46, 77, 0.05);
        }

        .mini-persona-add {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm);
            background: var(--primary);
            border: 1px solid var(--primary);
            border-radius: var(--radius-md);
            font-size: 11px;
            color: white;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .mini-persona-add:hover {
            background: var(--primary-dark);
            transform: scale(1.05);
        }

        .mini-avatar {
            width: 24px;
            height: 24px;
            background: var(--surface-lighter);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .mini-persona-add .mini-avatar {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-weight: 700;
        }

        /* Success Badge */
        .success-badge {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid var(--success);
            color: var(--success);
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
        }

        .ai-badge {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            background: rgba(255, 46, 77, 0.1);
            border: 1px solid var(--primary);
            color: var(--primary);
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
        }

        /* Preview Content Grid */
        .preview-content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .preview-post {
            background: var(--surface-light);
            border: 1px solid var(--border);
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            transition: all var(--transition-base);
        }

        .preview-post:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary);
        }

        .preview-post-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .preview-avatar {
            width: 40px;
            height: 40px;
            background: var(--surface-lighter);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .preview-meta {
            flex: 1;
        }

        .preview-author {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .preview-time {
            font-size: 12px;
            color: var(--text-tertiary);
        }

        .preview-image {
            width: 100%;
            height: 160px;
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .preview-image-placeholder {
            color: var(--text-tertiary);
            opacity: 0.5;
        }

        .preview-text h4 {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            line-height: 1.4;
        }

        .preview-text p {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-md);
        }

        .preview-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-lg);
        }

        .preview-tags span {
            padding: var(--spacing-xs) var(--spacing-sm);
            background: rgba(255, 46, 77, 0.1);
            color: var(--primary);
            font-size: 12px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--primary);
        }

        .preview-stats {
            display: flex;
            gap: var(--spacing-lg);
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border);
        }

        .preview-stat {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .preview-cta {
            text-align: center;
            padding: var(--spacing-xl);
            background: var(--surface-light);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border);
        }

        .preview-cta p {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
        }

        /* AI Capabilities */
        .ai-capabilities {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xl);
        }

        .capability-item {
            display: flex;
            gap: var(--spacing-lg);
            align-items: flex-start;
            padding: var(--spacing-lg);
            background: var(--surface-light);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            transition: all var(--transition-base);
        }

        .capability-item:hover {
            border-color: var(--primary);
            background: rgba(255, 46, 77, 0.03);
            transform: translateY(-2px);
        }

        .capability-icon {
            font-size: 24px;
            flex-shrink: 0;
            width: 48px;
            height: 48px;
            background: var(--surface);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .capability-content {
            flex: 1;
        }

        .capability-content h4 {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .capability-content p {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-md);
        }

        .capability-demo {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .demo-input, .demo-output {
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            font-size: 12px;
        }

        .demo-input {
            background: var(--surface);
            border: 1px solid var(--border);
            color: var(--text-secondary);
        }

        .demo-output {
            background: rgba(255, 46, 77, 0.1);
            border: 1px solid var(--primary);
            color: var(--primary);
            font-weight: 500;
        }

        /* Empty Dashboard State */
        .empty-dashboard-state {
            padding: var(--spacing-2xl) 0;
            text-align: center;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .empty-state-container {
            max-width: 500px;
            margin: 0 auto;
        }

        .empty-state-visual {
            margin-bottom: var(--spacing-2xl);
            position: relative;
        }

        .empty-icon-wrapper {
            position: relative;
            display: inline-block;
        }

        .empty-main-icon {
            color: var(--text-tertiary);
            opacity: 0.6;
            animation: float 6s ease-in-out infinite;
        }

        .empty-sparkles {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .sparkle {
            position: absolute;
            font-size: 16px;
            animation: sparkle 3s ease-in-out infinite;
        }

        .sparkle-1 {
            top: 10%;
            right: 20%;
            animation-delay: 0s;
        }

        .sparkle-2 {
            top: 20%;
            left: 15%;
            animation-delay: 1s;
        }

        .sparkle-3 {
            bottom: 15%;
            right: 10%;
            animation-delay: 2s;
        }

        @keyframes sparkle {
            0%, 100% { 
                opacity: 0; 
                transform: scale(0.5) rotate(0deg);
            }
            50% { 
                opacity: 1; 
                transform: scale(1) rotate(180deg);
            }
        }

        .empty-state-content {
            margin-bottom: var(--spacing-2xl);
        }

        .empty-state-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
        }

        .empty-state-description {
            font-size: 16px;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-2xl);
        }

        .empty-state-cta {
            padding: var(--spacing-lg) var(--spacing-2xl);
            font-size: 18px;
            font-weight: 600;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            box-shadow: var(--shadow-md);
            animation: pulse 3s ease-in-out infinite;
        }

        .empty-state-cta:hover {
            transform: translateY(-3px);
            box-shadow: var(--glow), var(--shadow-lg);
            animation: none;
        }

        /* Empty Content State */
        .empty-recent-content {
            padding: var(--spacing-2xl);
            text-align: center;
        }

        .empty-state-visual {
            max-width: 400px;
            margin: 0 auto;
        }

        .empty-content-illustration {
            position: relative;
            margin-bottom: var(--spacing-xl);
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .illustration-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .floating-icon {
            position: absolute;
            font-size: 20px;
            opacity: 0.3;
            animation: float 4s ease-in-out infinite;
        }

        .floating-icon:nth-child(1) {
            top: 20%;
            left: 20%;
            animation-delay: 0s;
        }

        .floating-icon:nth-child(2) {
            top: 10%;
            right: 20%;
            animation-delay: 1s;
        }

        .floating-icon:nth-child(3) {
            bottom: 20%;
            left: 30%;
            animation-delay: 2s;
        }

        .illustration-main {
            position: relative;
            z-index: 10;
        }

        .main-icon {
            color: var(--text-tertiary);
            opacity: 0.6;
        }

        .empty-state-text h3 {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .empty-state-text p {
            font-size: 16px;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-xl);
        }

        .empty-state-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
        }

        /* Trending Placeholder */
        .empty-trending-preview {
            padding: var(--spacing-lg);
        }

        .trending-placeholder-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
        }

        .trending-placeholder-item {
            position: relative;
            background: var(--surface-light);
            border: 1px dashed var(--border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .placeholder-content {
            opacity: 0.3;
        }

        .placeholder-line {
            height: 8px;
            background: var(--surface-lighter);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-xs);
        }

        .placeholder-line.long { width: 100%; }
        .placeholder-line.medium { width: 75%; }
        .placeholder-line.short { width: 50%; }

        .placeholder-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--text-tertiary);
            font-size: 12px;
            text-align: center;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .welcome-banner {
                grid-template-columns: 1fr;
                gap: var(--spacing-xl);
                text-align: center;
                min-height: auto;
                padding: var(--spacing-xl);
            }

            .banner-visual {
                order: -1;
            }

            .banner-illustration {
                width: 180px;
                height: 180px;
            }

            .ring-outer {
                width: 160px;
                height: 160px;
            }

            .ring-middle {
                width: 120px;
                height: 120px;
            }

            .ring-particle {
                width: 40px;
                height: 40px;
            }

            .particle-icon svg {
                width: 18px;
                height: 18px;
            }

            .illustration-core {
                width: 90px;
                height: 90px;
            }

            .core-icon svg {
                width: 40px;
                height: 40px;
            }

            .float-bubble {
                font-size: 10px;
                padding: 4px 8px;
            }

            .sparkle-star {
                font-size: 10px;
            }

            .banner-content {
                padding-left: 0;
            }

            .banner-title {
                font-size: 28px;
            }

            .banner-features {
                grid-template-columns: 1fr;
                gap: var(--spacing-sm);
                margin-bottom: var(--spacing-xl);
            }

            .banner-actions {
                flex-direction: column;
                align-items: center;
                gap: var(--spacing-md);
            }

            .banner-cta, .banner-secondary {
                width: 100%;
                max-width: 280px;
                justify-content: center;
            }

            .empty-state-title {
                font-size: 24px;
            }

            .empty-state-cta {
                width: 100%;
                max-width: 280px;
            }

            .empty-input-section {
                flex-direction: column;
            }

            .generation-input-disabled {
                width: 100%;
            }
        }
    </style>

    <script>
        function createFirstPersona() {
            showToast('Opening persona creation wizard...', 'info');
            // Simulate opening persona creation modal
            setTimeout(() => {
                showToast('Persona creation feature will be available soon!', 'success');
            }, 1000);
        }

        function showDemo() {
            showToast('Demo feature coming soon!', 'info');
        }

        function closeBanner() {
            const banner = document.getElementById('welcomeBanner');
            const emptyState = document.getElementById('emptyState');
            
            // Animate banner out
            banner.style.transform = 'translateY(-20px)';
            banner.style.opacity = '0';
            
            setTimeout(() => {
                banner.style.display = 'none';
                emptyState.style.display = 'block';
                emptyState.style.opacity = '0';
                emptyState.style.transform = 'translateY(20px)';
                
                // Animate empty state in
                setTimeout(() => {
                    emptyState.style.opacity = '1';
                    emptyState.style.transform = 'translateY(0)';
                }, 100);
            }, 300);
            
            showToast('Banner hidden. Create a persona to unlock all features!', 'info');
        }

        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            toast.innerHTML = `
                <div class="toast-icon">${icon}</div>
                <div class="toast-message">${message}</div>
            `;
            
            toastContainer.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // Mobile menu functionality
        document.getElementById('mobileMenuToggle').addEventListener('click', () => {
            document.getElementById('sidebar').classList.toggle('active');
        });

        // Navigation functionality
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                showToast('Create your first persona to unlock all features!', 'warning');
            });
        });

        // Add entrance animations
        document.addEventListener('DOMContentLoaded', () => {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.stat-card, .onboarding-step, .card').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'all 0.6s ease';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>