# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **"人设驱动"小红书内容助手** (Persona-Driven Xiaohongshu Content Assistant), positioned as your online social media specialist. Users upload photos on the web interface, configure personas, and AI automatically generates content based on photos and trending topics to maintain high-quality account output.

## Core Product Features

### 1. **One-Click Workflow**
- Upload photos to Camera Roll (manual selection, no auto-sync)
- Complete persona setup → Select information sources (Reddit, X, Douban, Xiaohongshu, etc.)
- Click "Generate Now": Optional keyword input or leave blank for AI auto-generation
- System generates complete posts (title, content, tags, images) ready for publishing

### 2. **Photo-Driven Content Creation**
- AI identifies and tags uploaded photos (scene, style, tone)
- Prioritizes "unused, most relevant" photos during generation
- Ensures visual freshness and content relevance

### 3. **Rich Persona Memory**
- Avatar, tone, brand info, posting goals set once
- AI consistently "speaks in character"
- Progress ring shows daily posting completion

### 4. **Controllable Information Scope**
- Sources panel for selecting/adding information sources
- Determines what the persona "usually reads"
- AI only processes content from selected sources

### 5. **Trending Inspiration Panel**
- Backend scrapes big data trends → filtered Recommended Ideas
- Click to auto-fill topics into input box

## Project Structure

- **index.html**: Main application structure
- **styles.css**: Complete styling system with dark theme
- **app.js**: Modular JavaScript application logic
- No build process or dependencies required
- Can be opened directly in a browser

## Development Commands

Since this is a static HTML/CSS/JS application:

1. Open `index.html` directly in a web browser
2. Use browser developer tools for debugging
3. Refresh the page to see changes
4. For better performance, use a local server: `python3 -m http.server 8000`

## Architecture

The application is structured as a single-page application with:

### **Core Components**:
- **Sidebar**: Persona switcher and navigation
- **Hero Bar**: Persona info and content generation input
- **Content Area**: Dynamic page loading system
- **Camera Roll**: Photo upload and management
- **Sources Panel**: Information source configuration

### **Key Features**:
- **Persona Management**: Multiple writing personas with unique characteristics
- **Photo-Driven Generation**: AI analyzes photos for content creation
- **Trending Integration**: Real-time hot topic recommendations
- **Progress Tracking**: Daily content creation goals
- **Responsive Design**: Mobile and desktop optimized

### **Technical Implementation**:
- **Modular JavaScript**: Easy feature addition and maintenance
- **CSS Custom Properties**: Consistent theming system
- **Dark Theme**: Eye-friendly night mode
- **Toast Notifications**: User feedback system
- **Modal System**: Overlay interactions

## Target Use Cases

1. **Personal Bloggers**: Rich material but lack time for copywriting
2. **Small Brands**: All posts centered around product photos
3. **Agencies**: Quick "virtual operation" persona setup for clients

## Experience Highlights

- **Responsive Web UI**: Sidebar navigation + hero bar progress ring
- **Dark Mode**: Eye-friendly night creation
- **Keyboard Shortcuts**: Enter to generate, Esc to close modals
- **One-Click Generation**: Upload photos → AI creates content with soul

## Development Notes

- The application simulates AI content generation with mockup data
- All interactions provide visual feedback through toast notifications
- The modular structure allows easy expansion of new features
- Photo analysis and trending data integration are placeholder implementations