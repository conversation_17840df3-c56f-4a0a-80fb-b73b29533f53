// Xiaohongshu AI Writer - Main Application
class XiaohongshuApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.currentPersona = 'lifestyle';
        this.personas = {
            lifestyle: {
                name: '小雅',
                emoji: '🌸',
                gender: '女',
                mbti: 'ENFP',
                personality: '活泼开朗、充满好奇心、热爱生活',
                introduction: '一个热爱生活的95后女孩，喜欢记录日常中的美好瞬间。对美食、旅行和摄影都有着浓厚的兴趣，相信生活中处处都有值得分享的小确幸。',
                style: 'casual',
                topics: ['日常生活', '美食', '旅行', '心情分享']
            },
            fashion: {
                name: '时尚芊芊',
                emoji: '👗',
                gender: '女',
                mbti: 'ESFJ',
                personality: '时尚敏感、善于搭配、注重细节',
                introduction: '时尚博主，专注于分享实用的穿搭技巧和美妆心得。相信每个人都可以通过合适的搭配展现自己的独特魅力，让时尚变得更加亲民和实用。',
                style: 'trendy',
                topics: ['穿搭', '美妆', '购物', '时尚趋势']
            },
            food: {
                name: '美食小陈',
                emoji: '🍜',
                gender: '女',
                mbti: 'ISFJ',
                personality: '细心温柔、热爱烹饪、享受分享',
                introduction: '美食爱好者，擅长发现城市中的隐藏美食和制作简单易学的家常菜。希望通过分享美食，让更多人感受到烹饪和品尝的乐趣。',
                style: 'foodie',
                topics: ['美食', '探店', '烹饪', '餐厅推荐']
            }
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadPage('dashboard');
        this.setupMobileMenu();
        this.setupDragAndDrop();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.loadPage(page);
                this.setActiveNav(item);
            });
        });

        // Persona switching
        document.querySelectorAll('.persona-card').forEach(card => {
            card.addEventListener('click', () => {
                this.switchPersona(card.dataset.persona);
            });
        });

        // Add persona button
        document.getElementById('addPersonaBtn').addEventListener('click', () => {
            this.showModal('addPersona');
        });
    }

    setupMobileMenu() {
        const toggle = document.getElementById('mobileMenuToggle');
        const sidebar = document.getElementById('sidebar');
        
        toggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!sidebar.contains(e.target) && !toggle.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        });
    }

    loadPage(pageName) {
        this.currentPage = pageName;
        const mainContent = document.getElementById('mainContent');
        
        switch(pageName) {
            case 'dashboard':
                mainContent.innerHTML = this.getDashboardPage();
                break;
            case 'content':
                mainContent.innerHTML = this.getContentPage();
                break;
            case 'gallery':
                mainContent.innerHTML = this.getGalleryPage();
                break;
            case 'sources':
                mainContent.innerHTML = this.getSourcesPage();
                this.setupDragAndDrop();
                break;
            case 'settings':
                mainContent.innerHTML = this.getSettingsPage();
                break;
        }
    }

    getDashboardPage() {
        const persona = this.personas[this.currentPersona];
        return `
            <div class="page-container">
                <div class="header-bar">
                    <div class="header-content">
                        <h1 class="page-title">主页</h1>
                        <button class="btn btn-primary" onclick="app.loadPage('content')">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"/>
                            </svg>
                            开始创作
                        </button>
                    </div>
                </div>
                
                <div class="content-section">
                    <!-- 人物角色详细信息 -->
                    <div class="persona-detail-card">
                        <div class="persona-main-layout">
                            <div class="persona-left">
                                <div class="persona-card-compact">
                                    <div class="persona-avatar-compact">
                                        ${persona.emoji}
                                    </div>
                                    <div class="persona-info-compact">
                                        <h2 class="persona-name-compact">${persona.name}</h2>
                                        <div class="persona-tags">
                                            <span class="persona-tag gender-tag">${persona.gender}</span>
                                            <span class="persona-tag mbti-tag">${persona.mbti}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="persona-stats">
                                    <div class="stat-compact">
                                        <div class="stat-number">24</div>
                                        <div class="stat-label-compact">内容</div>
                                    </div>
                                    <div class="stat-compact">
                                        <div class="stat-number">18</div>
                                        <div class="stat-label-compact">已发布</div>
                                    </div>
                                    <div class="stat-compact">
                                        <div class="stat-number">6</div>
                                        <div class="stat-label-compact">草稿</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="persona-right">
                                <div class="persona-introduction">
                                    <h3 class="introduction-title">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"/>
                                        </svg>
                                        角色介绍
                                    </h3>
                                    
                                    <div class="introduction-content">
                                        <div class="intro-section">
                                            <h4 class="intro-subtitle">性格特点</h4>
                                            <p class="intro-text">${persona.personality}</p>
                                        </div>
                                        
                                        <div class="intro-section">
                                            <h4 class="intro-subtitle">详细介绍</h4>
                                            <p class="intro-text">${persona.introduction}</p>
                                        </div>
                                        
                                        <div class="intro-section">
                                            <h4 class="intro-subtitle">擅长话题</h4>
                                            <div class="topics-grid">
                                                ${persona.topics.map(topic => `
                                                    <span class="topic-tag">${topic}</span>
                                                `).join('')}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据统计 -->
                    <div class="grid grid-cols-2 gap-lg mb-lg">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">📊 内容统计</h3>
                            </div>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value">24</div>
                                    <div class="stat-label">总共生成</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">18</div>
                                    <div class="stat-label">已发布</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">6</div>
                                    <div class="stat-label">草稿</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">📸 照片使用情况</h3>
                            </div>
                            <div class="photo-usage">
                                <div class="flex-between mb-sm">
                                    <span style="font-size: 14px;">已使用照片</span>
                                    <span style="font-size: 14px; color: var(--primary);">8/24</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 33%;"></div>
                                </div>
                                <div class="mt-md">
                                    <button class="btn btn-primary btn-photo-manage" onclick="app.loadPage('gallery')">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"/>
                                        </svg>
                                        管理照片库
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 最近生成的内容 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">📝 最近生成的内容</h3>
                            <button class="btn btn-ghost" onclick="app.loadPage('content')">查看全部</button>
                        </div>
                        <div class="recent-content-list">
                            ${this.getRecentContent()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getContentPage() {
        return `
            <div class="page-container">
                <div class="header-bar">
                    <div class="header-content">
                        <h1 class="page-title">创作台</h1>
                        <div class="generate-panel-compact">
                            <input type="text" class="generate-input-compact" placeholder="输入主题或让AI智能推荐..." id="topicInput">
                            <button class="btn btn-primary generate-btn-compact" onclick="app.generateWithTopic()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 0116 0zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"/>
                                </svg>
                                Generate Now
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="content-section">
                    <!-- 使用说明 -->
                    <div class="card mb-lg">
                        <div class="card-header">
                            <h3 class="card-title">🚀 如何使用</h3>
                            <button class="btn btn-ghost" onclick="app.toggleGenerationSteps()" id="toggleSteps">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
                                </svg>
                                展开
                            </button>
                        </div>
                        <div class="generation-steps" id="generationSteps" style="display: none;">
                            <div class="step-item">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h4 class="step-title">📸 上传照片</h4>
                                    <p class="step-description">前往Camera Roll上传你的照片，AI会基于照片内容生成相关文案</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h4 class="step-title">🎯 选择主题</h4>
                                    <p class="step-description">从创作建议中选择感兴趣的主题，或在输入框中自定义主题</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h4 class="step-title">✨ 一键生成</h4>
                                    <p class="step-description">点击Generate Now按钮，AI会自动生成完整的小红书内容</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 创作建议 -->
                    <div class="card mb-lg">
                        <div class="card-header">
                            <h3 class="card-title">💡 创作建议</h3>
                            <span style="color: var(--text-secondary); font-size: 14px;">点击选择主题，一键生成内容</span>
                        </div>
                        <div class="ideas-grid-home">
                            ${this.getCreationIdeas()}
                        </div>
                    </div>
                    
                    <!-- 生成历史 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">📝 生成历史</h3>
                            <div class="flex gap-md">
                                <select class="form-input" style="width: 120px;" id="contentFilter">
                                    <option>全部内容</option>
                                    <option>已发布</option>
                                    <option>草稿</option>
                                </select>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-lg">
                            ${this.getAllContent()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getAllContent() {
        const allContent = [
            {
                title: '春日野餐好时光🌸',
                content: '今天天气真的太好了！约上三五好友一起去公园野餐...',
                photo: '春日野餐.jpg',
                tags: ['#春日野餐', '#生活分享', '#周末时光'],
                time: '10分钟前',
                status: 'published'
            },
            {
                title: '咖啡店探店｜这家真的绝了',
                content: '发现了一家超棒的咖啡店，环境安静，咖啡香醇...',
                photo: '咖啡拍摄.jpg',
                tags: ['#咖啡探店', '#美食分享', '#城市漫步'],
                time: '2小时前',
                status: 'draft'
            },
            {
                title: '极简穿搭分享💫',
                content: '最近爱上了极简风格，分享今天的穿搭...',
                photo: '穿搭展示.jpg',
                tags: ['#穿搭分享', '#极简生活', '#时尚'],
                time: '昨天',
                status: 'published'
            },
            {
                title: '周末读书笔记📚',
                content: '最近在读《生活的艺术》，有很多感悟想要分享...',
                photo: '读书笔记.jpg',
                tags: ['#读书笔记', '#周末时光', '#个人成长'],
                time: '2天前',
                status: 'published'
            },
            {
                title: '家居布置小技巧',
                content: '分享一些让家里变得更温馨的小方法...',
                photo: '居家布置.jpg',
                tags: ['#家居', '#生活美学', '#装饰'],
                time: '3天前',
                status: 'published'
            },
            {
                title: '早餐日记：健康从早开始',
                content: '一日之计在于晨，分享我的营养早餐搭配...',
                photo: '美食摄影.jpg',
                tags: ['#早餐', '#健康生活', '#美食'],
                time: '4天前',
                status: 'published'
            }
        ];
        
        return allContent.map(item => `
            <div class="generated-content-card">
                <div class="content-header">
                    <h3 style="font-size: 18px; font-weight: 600;">${item.title}</h3>
                    <span class="badge badge-${item.status === 'published' ? 'success' : 'secondary'}">
                        ${item.status === 'published' ? '已发布' : '草稿'}
                    </span>
                </div>
                <p style="color: var(--text-secondary); margin: 12px 0;">${item.content}</p>
                <div class="content-meta">
                    <div class="flex gap-sm" style="flex-wrap: wrap;">
                        ${item.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                    <div style="font-size: 12px; color: var(--text-tertiary);">
                        <span>使用照片: ${item.photo}</span> · <span>${item.time}</span>
                    </div>
                </div>
                <div class="flex gap-sm mt-md">
                    <button class="btn btn-ghost" onclick="app.viewContent('${item.title}')">查看详情</button>
                    <button class="btn btn-ghost" onclick="app.regenerateContent('${item.title}')">重新生成</button>
                    ${item.status === 'draft' ? `<button class="btn btn-primary" onclick="app.publishDraft('${item.title}')">发布</button>` : ''}
                </div>
            </div>
        `).join('');
    }




    getTrendingPage() {
        return `
            <div class="page-container">
                <div class="header-bar">
                    <div class="header-content">
                        <h1 class="page-title">热门趋势</h1>
                        <div class="flex gap-md">
                            <button class="btn btn-secondary" onclick="app.refreshTrends()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"/>
                                </svg>
                                刷新
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="content-section">
                    <div class="grid grid-cols-2 gap-xl">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🔥 热门话题</h3>
                            </div>
                            <div class="trending-list">
                                ${this.getTrendingTopics()}
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">📈 上升趋势</h3>
                            </div>
                            <div class="trending-list">
                                ${this.getRisingTrends()}
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-lg">
                        <div class="card-header">
                            <h3 class="card-title">💡 创作建议</h3>
                        </div>
                        <div class="grid grid-cols-2 gap-lg">
                            ${this.getContentSuggestions()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Helper methods for generating content
    getCreationIdeas() {
        const ideas = [
            { 
                title: '2025年春季穿搭灵感：复古风回潮', 
                description: '复古元素强势回归，千禧风格与Y2K美学融合，打造独特个人风格...',
                source: '小红书热门话题', 
                icon: '📱',
                trend: 'hot'
            },
            { 
                title: '极简生活美学：断舍离后的品质升级', 
                description: '探索极简主义生活方式，从物质到精神的全面升级，打造高品质生活空间...',
                source: '豆瓣生活组', 
                icon: '📚',
                trend: 'warm'
            },
            { 
                title: '周末探店：隐藏在城市角落的宝藏小店', 
                description: '发现城市中的小众店铺，从vintage古着到独立咖啡馆，记录独特的城市体验...',
                source: '小红书探店话题', 
                icon: '📱',
                trend: 'warm'
            },
            { 
                title: '春日野餐攻略：ins风格拍照指南', 
                description: '春天来了，野餐季节开启！分享拍照技巧和野餐装备推荐...',
                source: '小红书生活分享', 
                icon: '📱',
                trend: 'hot'
            },
            { 
                title: '居家办公好物推荐', 
                description: '分享提升工作效率的居家办公神器，让在家工作更舒适...',
                source: 'Reddit生活组', 
                icon: '🌐',
                trend: 'cool'
            },
            { 
                title: '简单易学的美食制作教程', 
                description: '零基础也能做出美味料理，分享简单易上手的美食制作方法...',
                source: 'X美食话题', 
                icon: '✖️',
                trend: 'warm'
            }
        ];
        
        return ideas.map(idea => `
            <div class="idea-card-home" onclick="app.selectCreationIdea('${idea.title}')">
                <div class="idea-header-home">
                    <h3 class="idea-title-home">${idea.title}</h3>
                    <span class="heat-badge ${idea.trend}">
                        ${idea.trend === 'hot' ? '🔥' : idea.trend === 'warm' ? '⭐' : '💡'}
                        ${idea.trend.toUpperCase()}
                    </span>
                </div>
                <p class="idea-description-home">${idea.description}</p>
                <div class="idea-source-home">
                    <span class="source-icon">${idea.icon}</span>
                    <span>${idea.source}</span>
                </div>
            </div>
        `).join('');
    }

    getRecommendedIdeas() {
        const ideas = [
            { title: '2025年春季穿搭灵感：复古风回潮', trend: 'hot', source: '小红书热门话题', icon: '🔥' },
            { title: '极简生活美学：断舍离后的品质升级', trend: 'warm', source: '豆瓣生活组', icon: '✨' },
            { title: '周末探店：隐藏在城市角落的宝藏小店', trend: 'warm', source: '小红书探店话题', icon: '🌟' },
            { title: '春日野餐攻略：ins风格拍照指南', trend: 'hot', source: '小红书生活分享', icon: '🔥' },
            { title: '居家办公好物推荐', trend: 'cool', source: 'Reddit生活组', icon: '💡' },
            { title: '简单易学的美食制作教程', trend: 'warm', source: 'X美食话题', icon: '✨' }
        ];
        
        return ideas.map(idea => `
            <div class="idea-card" onclick="app.selectIdea('${idea.title}')">
                <div class="idea-header">
                    <h4 class="idea-title">${idea.title}</h4>
                    <span class="heat-badge ${idea.trend}">
                        ${idea.icon}
                        ${idea.trend.toUpperCase()}
                    </span>
                </div>
                <div class="idea-source">
                    <span class="source-icon">📱</span>
                    <span>${idea.source}</span>
                </div>
            </div>
        `).join('');
    }

    getRecentContent() {
        const recentContent = [
            { title: '春日野餐好时光🌸', status: 'published', time: '10分钟前' },
            { title: '咖啡店探店｜这家真的绝了', status: 'draft', time: '2小时前' },
            { title: '极简穿搭分享💫', status: 'published', time: '昨天' },
            { title: '周末读书笔记📚', status: 'published', time: '2天前' }
        ];
        
        return recentContent.map(item => `
            <div class="recent-item" onclick="app.viewContent('${item.title}')">
                <div class="flex-between">
                    <div>
                        <h5 class="recent-title">${item.title}</h5>
                        <span class="recent-time">${item.time}</span>
                    </div>
                    <span class="badge badge-${item.status === 'published' ? 'success' : 'secondary'}">
                        ${item.status === 'published' ? '已发布' : '草稿'}
                    </span>
                </div>
            </div>
        `).join('');
    }

    getRecommendedTopics() {
        const topics = [
            { title: '春日野餐攻略', trend: 'hot', desc: '分享户外野餐的准备和拍照技巧' },
            { title: '居家办公好物', trend: 'warm', desc: '推荐提升工作效率的小物件' },
            { title: '简约穿搭分享', trend: 'cool', desc: '展示日常简约风格的搭配' },
            { title: '美食制作教程', trend: 'warm', desc: '分享简单易学的美食制作方法' }
        ];
        
        return topics.map(topic => `
            <div class="topic-card" onclick="app.generateContent('${topic.title}')">
                <div class="flex-between mb-sm">
                    <h4 style="font-size: 16px; font-weight: 600;">${topic.title}</h4>
                    <span class="badge badge-${topic.trend}">${topic.trend.toUpperCase()}</span>
                </div>
                <p style="color: var(--text-secondary); font-size: 14px;">${topic.desc}</p>
            </div>
        `).join('');
    }


    getTrendingTopics() {
        const topics = [
            { name: '#春日穿搭', posts: '2.3万', trend: '+15%' },
            { name: '#美食探店', posts: '1.8万', trend: '+12%' },
            { name: '#居家好物', posts: '1.5万', trend: '+8%' },
            { name: '#旅行日记', posts: '1.2万', trend: '+23%' },
            { name: '#护肤心得', posts: '950', trend: '+19%' }
        ];
        
        return topics.map((topic, index) => `
            <div class="trending-item" onclick="app.useHashtag('${topic.name}')">
                <div class="flex-between">
                    <span style="font-weight: 600;">${index + 1}. ${topic.name}</span>
                    <span class="badge badge-success">${topic.trend}</span>
                </div>
                <p style="color: var(--text-secondary); font-size: 12px;">${topic.posts} 篇内容</p>
            </div>
        `).join('');
    }

    getRisingTrends() {
        const trends = [
            { name: '#极简生活', posts: '560', trend: '+45%' },
            { name: '#数码好物', posts: '423', trend: '+38%' },
            { name: '#健康饮食', posts: '712', trend: '+33%' },
            { name: '#阅读笔记', posts: '298', trend: '+52%' },
            { name: '#宠物日常', posts: '634', trend: '+29%' }
        ];
        
        return trends.map((trend, index) => `
            <div class="trending-item" onclick="app.useHashtag('${trend.name}')">
                <div class="flex-between">
                    <span style="font-weight: 600;">${index + 1}. ${trend.name}</span>
                    <span class="badge badge-warning">${trend.trend}</span>
                </div>
                <p style="color: var(--text-secondary); font-size: 12px;">${trend.posts} 篇内容</p>
            </div>
        `).join('');
    }

    getContentSuggestions() {
        const suggestions = [
            { title: '春季护肤攻略', reason: '季节相关，搜索量上升' },
            { title: '居家运动指南', reason: '健康话题，持续热门' },
            { title: '简单家常菜', reason: '实用内容，用户需求高' },
            { title: '工作效率提升', reason: '职场话题，关注度高' }
        ];
        
        return suggestions.map(suggestion => `
            <div class="suggestion-card" onclick="app.generateContent('${suggestion.title}')">
                <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 8px;">${suggestion.title}</h4>
                <p style="color: var(--text-secondary); font-size: 14px;">${suggestion.reason}</p>
            </div>
        `).join('');
    }

    // Additional page methods
    getGalleryPage() {
        return `
            <div class="page-container">
                <div class="header-bar">
                    <div class="header-content">
                        <h1 class="page-title">Camera Roll</h1>
                        <div class="flex gap-md">
                            <span style="color: var(--text-secondary); font-size: 14px;">手动选择素材，无自动同步</span>
                            <button class="btn btn-secondary" onclick="app.uploadMedia()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z"/>
                                </svg>
                                上传照片
                            </button>
                            <button class="btn btn-primary" onclick="app.analyzePhotos()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                AI分析标签
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="content-section">
                    <div class="card mb-lg">
                        <div class="card-header">
                            <h3 class="card-title">AI照片分析</h3>
                            <span style="color: var(--text-secondary); font-size: 14px;">自动识别场景、风格、色调</span>
                        </div>
                        <div class="flex gap-md mb-md">
                            <button class="btn btn-ghost">全部 (24)</button>
                            <button class="btn btn-ghost">已使用 (8)</button>
                            <button class="btn btn-ghost">未使用 (16)</button>
                            <button class="btn btn-ghost">生活方式 (12)</button>
                            <button class="btn btn-ghost">美食 (6)</button>
                            <button class="btn btn-ghost">时尚 (6)</button>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-4 gap-md">
                        ${this.getMediaGridWithTags()}
                    </div>
                </div>
            </div>
        `;
    }

    getSourcesPage() {
        return `
            <div class="page-container">
                <div class="header-bar">
                    <div class="header-content">
                        <h1 class="page-title">信息源配置</h1>
                        <div class="flex gap-md">
                            <span style="color: var(--text-secondary); font-size: 14px;">决定AI"平时都看什么"</span>
                            <button class="btn btn-primary" onclick="app.addCustomSource()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"/>
                                </svg>
                                添加自定义源
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="content-section">
                    <div class="card mb-lg">
                        <div class="card-header">
                            <h3 class="card-title">内置信息源</h3>
                            <span style="color: var(--text-secondary); font-size: 14px;">选择AI学习的内容来源</span>
                        </div>
                        <div class="grid grid-cols-2 gap-lg">
                            <div class="source-category">
                                <h4 style="margin-bottom: 16px; color: var(--primary);">🌐 国际平台</h4>
                                <div class="source-list">
                                    <label class="source-item">
                                        <input type="checkbox" checked>
                                        <div class="source-info">
                                            <div class="source-name">Reddit</div>
                                            <div class="source-desc">热门社区讨论</div>
                                        </div>
                                        <div class="source-status">已连接</div>
                                    </label>
                                    <label class="source-item">
                                        <input type="checkbox" checked>
                                        <div class="source-info">
                                            <div class="source-name">X (Twitter)</div>
                                            <div class="source-desc">实时话题趋势</div>
                                        </div>
                                        <div class="source-status">已连接</div>
                                    </label>
                                    <label class="source-item">
                                        <input type="checkbox">
                                        <div class="source-info">
                                            <div class="source-name">Instagram</div>
                                            <div class="source-desc">视觉内容灵感</div>
                                        </div>
                                        <div class="source-status">未连接</div>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="source-category">
                                <h4 style="margin-bottom: 16px; color: var(--primary);">🇨🇳 国内平台</h4>
                                <div class="source-list">
                                    <label class="source-item">
                                        <input type="checkbox" checked>
                                        <div class="source-info">
                                            <div class="source-name">小红书</div>
                                            <div class="source-desc">生活方式内容</div>
                                        </div>
                                        <div class="source-status">已连接</div>
                                    </label>
                                    <label class="source-item">
                                        <input type="checkbox" checked>
                                        <div class="source-info">
                                            <div class="source-name">豆瓣</div>
                                            <div class="source-desc">文艺生活分享</div>
                                        </div>
                                        <div class="source-status">已连接</div>
                                    </label>
                                    <label class="source-item">
                                        <input type="checkbox">
                                        <div class="source-info">
                                            <div class="source-name">微博</div>
                                            <div class="source-desc">热门话题追踪</div>
                                        </div>
                                        <div class="source-status">未连接</div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mb-lg">
                        <div class="card-header">
                            <h3 class="card-title">自定义信息源</h3>
                            <span style="color: var(--text-secondary); font-size: 14px;">添加特定网站或RSS源</span>
                        </div>
                        <div class="custom-sources">
                            <div class="custom-source-item">
                                <div class="flex-between">
                                    <div>
                                        <div style="font-weight: 600;">科技媒体RSS</div>
                                        <div style="color: var(--text-secondary); font-size: 14px;">https://techcrunch.com/feed/</div>
                                    </div>
                                    <button class="btn btn-ghost" onclick="app.removeSource('rss1')">移除</button>
                                </div>
                            </div>
                            <div class="custom-source-item">
                                <div class="flex-between">
                                    <div>
                                        <div style="font-weight: 600;">设计灵感网站</div>
                                        <div style="color: var(--text-secondary); font-size: 14px;">https://dribbble.com</div>
                                    </div>
                                    <button class="btn btn-ghost" onclick="app.removeSource('custom1')">移除</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 知识库上传 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">📚 知识库上传</h3>
                            <span style="color: var(--text-secondary); font-size: 14px;">上传文档作为AI创作的参考资料</span>
                        </div>
                        <div class="knowledge-upload-section">
                            <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                                <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.txt,.md,.json" style="display: none;" onchange="app.handleFileUpload(event)">
                                <div class="upload-icon">📎</div>
                                <div class="upload-text">
                                    <div style="font-weight: 600; margin-bottom: 8px;">点击或拖拽文件到此处</div>
                                    <div style="color: var(--text-secondary); font-size: 14px;">
                                        支持 PDF、Word、TXT、Markdown、JSON 格式
                                    </div>
                                </div>
                            </div>
                            
                            <div class="uploaded-files" id="uploadedFiles">
                                ${this.getUploadedFiles()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }



    getSettingsPage() {
        return `
            <div class="page-container">
                <div class="header-bar">
                    <div class="header-content">
                        <h1 class="page-title">设置</h1>
                    </div>
                </div>
                
                <div class="content-section">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">个人设置</h3>
                        </div>
                        <div class="form-group">
                            <label class="form-label">用户名称</label>
                            <input type="text" class="form-input" placeholder="例如：小红书创作者" id="userName" value="小红书创作者">
                        </div>
                        <div class="grid grid-cols-2 gap-md">
                            <div class="form-group">
                                <label class="form-label">性别</label>
                                <select class="form-input" id="userGender">
                                    <option value="女">女</option>
                                    <option value="男">男</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">MBTI类型</label>
                                <select class="form-input" id="userMBTI">
                                    <option value="ENFP">ENFP - 竞选者</option>
                                    <option value="INFP">INFP - 调停者</option>
                                    <option value="ENFJ">ENFJ - 主人公</option>
                                    <option value="INFJ">INFJ - 提倡者</option>
                                    <option value="ENTP">ENTP - 辩论家</option>
                                    <option value="INTP">INTP - 逻辑学家</option>
                                    <option value="ENTJ">ENTJ - 指挥官</option>
                                    <option value="INTJ">INTJ - 建筑师</option>
                                    <option value="ESFP">ESFP - 娱乐家</option>
                                    <option value="ISFP">ISFP - 探险家</option>
                                    <option value="ESFJ">ESFJ - 执政官</option>
                                    <option value="ISFJ">ISFJ - 守护者</option>
                                    <option value="ESTP">ESTP - 企业家</option>
                                    <option value="ISTP">ISTP - 鉴赏家</option>
                                    <option value="ESTJ">ESTJ - 总经理</option>
                                    <option value="ISTJ">ISTJ - 物流师</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">头像表情</label>
                            <input type="text" class="form-input" placeholder="选择一个表情符号，例如：🌸" id="userEmoji" value="🌸">
                        </div>
                        <div class="form-group">
                            <label class="form-label">性格特点</label>
                            <input type="text" class="form-input" placeholder="例如：活泼开朗、充满好奇心、热爱生活" id="userPersonality" value="活泼开朗、充满好奇心、热爱生活">
                        </div>
                        <div class="form-group">
                            <label class="form-label">个人介绍</label>
                            <textarea class="form-textarea" placeholder="详细描述你的背景、兴趣爱好、创作风格等..." id="userIntroduction">热爱分享生活中的美好瞬间，喜欢记录日常点滴，用文字和照片传递正能量。</textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">擅长话题</label>
                            <input type="text" class="form-input" placeholder="用逗号分隔，例如：日常生活,美食,旅行,心情分享" id="userTopics" value="日常生活,美食,旅行,心情分享">
                        </div>
                        <div class="flex gap-md">
                            <button class="btn btn-ghost" onclick="app.resetSettings()">重置</button>
                            <button class="btn btn-primary" onclick="app.saveSettings()">保存设置</button>
                        </div>
                    </div>
                    
                    <!-- AI助手设置 -->
                    <div class="card mb-lg">
                        <div class="card-header">
                            <h3 class="card-title">AI助手设置</h3>
                        </div>
                        <div class="form-group">
                            <label class="form-label">写作风格</label>
                            <select class="form-input" id="writingStyle">
                                <option value="warm">温馨亲切</option>
                                <option value="professional">专业严谨</option>
                                <option value="lively">活泼有趣</option>
                                <option value="artistic">文艺清新</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">内容长度偏好</label>
                            <select class="form-input" id="contentLength">
                                <option value="short">简短精炼</option>
                                <option value="medium">中等长度</option>
                                <option value="long">详细完整</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">创作领域</label>
                            <div class="flex gap-sm" style="flex-wrap: wrap;">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked> 生活方式
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox"> 时尚美妆
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox"> 美食探店
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox"> 旅行摄影
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 通知设置 -->
                    <div class="card mb-lg">
                        <div class="card-header">
                            <h3 class="card-title">通知设置</h3>
                        </div>
                        <div class="setting-item">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div>
                                <div>新趋势提醒</div>
                                <div style="font-size: 12px; color: var(--text-tertiary);">热门话题推送</div>
                            </div>
                        </div>
                        <div class="setting-item">
                            <label class="switch">
                                <input type="checkbox">
                                <span class="slider"></span>
                            </label>
                            <div>
                                <div>定时提醒</div>
                                <div style="font-size: 12px; color: var(--text-tertiary);">创作计划提醒</div>
                            </div>
                        </div>
                        <div class="setting-item">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div>
                                <div>生成完成通知</div>
                                <div style="font-size: 12px; color: var(--text-tertiary);">内容生成成功提醒</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据管理 -->
                    <div class="card mb-lg">
                        <div class="card-header">
                            <h3 class="card-title">数据管理</h3>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-secondary" style="width: 100%; margin-bottom: 12px;" onclick="app.exportData()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"/>
                                </svg>
                                导出数据
                            </button>
                            <button class="btn btn-ghost" style="width: 100%; margin-bottom: 12px;" onclick="app.clearCache()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 3a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z"/>
                                </svg>
                                清空缓存
                            </button>
                            <button class="btn btn-ghost" style="width: 100%; margin-bottom: 12px;" onclick="app.backupData()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z"/>
                                </svg>
                                备份数据
                            </button>
                        </div>
                    </div>
                    
                    <!-- 账号管理 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">账号管理</h3>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-ghost" style="width: 100%; margin-bottom: 12px;" onclick="app.changePassword()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"/>
                                </svg>
                                修改密码
                            </button>
                            <button class="btn btn-ghost" style="width: 100%; margin-bottom: 12px;" onclick="app.deleteAccount()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm5 2a1 1 0 00-1 1v3a1 1 0 102 0V8a1 1 0 00-1-1zm3 1a1 1 0 10-2 0v3a1 1 0 102 0V8z"/>
                                </svg>
                                删除账号
                            </button>
                            <button class="btn btn-ghost" style="width: 100%; color: var(--error);" onclick="app.logout()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z"/>
                                </svg>
                                退出登录
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Utility methods
    switchPersona(personaKey) {
        this.currentPersona = personaKey;
        document.querySelectorAll('.persona-card').forEach(card => {
            card.classList.remove('active');
        });
        document.querySelector(`[data-persona="${personaKey}"]`).classList.add('active');
        
        // Reload current page to update persona-specific content
        this.loadPage(this.currentPage);
        this.showToast('已切换到' + this.personas[personaKey].name, 'success');
    }

    setActiveNav(activeItem) {
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        activeItem.classList.add('active');
    }

    // Feature methods (mockup implementations)

    generateWithTopic() {
        const topic = document.getElementById('topicInput')?.value || '';
        if (topic) {
            this.showToast(`正在生成关于"${topic}"的内容...`, 'info');
        } else {
            this.showToast('正在基于照片和热点自动生成内容...', 'info');
        }
        setTimeout(() => {
            this.showToast('内容生成完成！', 'success');
            // Refresh the content list
            this.loadPage('content');
        }, 3000);
    }

    saveDraft() {
        this.showToast('草稿已保存', 'success');
    }

    publishContent() {
        this.showModal('publishConfirm');
    }


    useHashtag(hashtag) {
        this.showToast(`正在基于话题 ${hashtag} 生成内容...`, 'info');
        setTimeout(() => {
            this.loadPage('content');
            this.showToast('话题相关内容已生成！', 'success');
        }, 2000);
    }

    generateContent(topic) {
        this.showToast(`正在生成内容: ${topic}`, 'info');
        setTimeout(() => {
            this.loadPage('content');
            this.showToast('内容生成完成！', 'success');
        }, 2000);
    }

    // Toast notification system
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            <span>${message}</span>
        `;
        
        document.getElementById('toastContainer').appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // Modal system
    showModal(modalType) {
        const modalContainer = document.getElementById('modalContainer');
        
        let modalContent = '';
        switch(modalType) {
            case 'addPersona':
                modalContent = this.getAddPersonaModal();
                break;
            case 'publishConfirm':
                modalContent = this.getPublishConfirmModal();
                break;
            case 'addSource':
                modalContent = this.getAddSourceModal();
                break;
        }
        
        modalContainer.innerHTML = `
            <div class="modal-backdrop" onclick="app.closeModal()">
                <div class="modal" onclick="event.stopPropagation()">
                    ${modalContent}
                </div>
            </div>
        `;
    }

    closeModal() {
        document.getElementById('modalContainer').innerHTML = '';
    }

    getAddPersonaModal() {
        return `
            <div class="modal-header">
                <h3 class="modal-title">添加新身份</h3>
                <button class="modal-close" onclick="app.closeModal()">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"/>
                    </svg>
                </button>
            </div>
            <div class="form-group">
                <label class="form-label">身份名称</label>
                <input type="text" class="form-input" placeholder="例如：小雅" id="personaName">
            </div>
            <div class="grid grid-cols-2 gap-md">
                <div class="form-group">
                    <label class="form-label">性别</label>
                    <select class="form-input" id="personaGender">
                        <option value="女">女</option>
                        <option value="男">男</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">MBTI类型</label>
                    <select class="form-input" id="personaMBTI">
                        <option value="ENFP">ENFP - 竞选者</option>
                        <option value="INFP">INFP - 调停者</option>
                        <option value="ENFJ">ENFJ - 主人公</option>
                        <option value="INFJ">INFJ - 提倡者</option>
                        <option value="ENTP">ENTP - 辩论家</option>
                        <option value="INTP">INTP - 逻辑学家</option>
                        <option value="ENTJ">ENTJ - 指挥官</option>
                        <option value="INTJ">INTJ - 建筑师</option>
                        <option value="ESFP">ESFP - 娱乐家</option>
                        <option value="ISFP">ISFP - 探险家</option>
                        <option value="ESFJ">ESFJ - 执政官</option>
                        <option value="ISFJ">ISFJ - 守护者</option>
                        <option value="ESTP">ESTP - 企业家</option>
                        <option value="ISTP">ISTP - 鉴赏家</option>
                        <option value="ESTJ">ESTJ - 总经理</option>
                        <option value="ISTJ">ISTJ - 物流师</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">头像表情</label>
                <input type="text" class="form-input" placeholder="选择一个表情符号，例如：🌸" id="personaEmoji">
            </div>
            <div class="form-group">
                <label class="form-label">性格特点</label>
                <input type="text" class="form-input" placeholder="例如：活泼开朗、充满好奇心、热爱生活" id="personaPersonality">
            </div>
            <div class="form-group">
                <label class="form-label">角色介绍</label>
                <textarea class="form-textarea" placeholder="详细描述这个角色的背景、兴趣爱好、创作风格等..." id="personaIntroduction"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">擅长话题</label>
                <input type="text" class="form-input" placeholder="用逗号分隔，例如：日常生活,美食,旅行,心情分享" id="personaTopics">
            </div>
            <div class="flex gap-md">
                <button class="btn btn-ghost" onclick="app.closeModal()">取消</button>
                <button class="btn btn-primary" onclick="app.createPersona()">创建</button>
            </div>
        `;
    }

    getPublishConfirmModal() {
        return `
            <div class="modal-header">
                <h3 class="modal-title">发布内容</h3>
                <button class="modal-close" onclick="app.closeModal()">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"/>
                    </svg>
                </button>
            </div>
            <p style="margin-bottom: 24px;">确定要发布这篇内容吗？发布后将对所有人可见。</p>
            <div class="flex gap-md">
                <button class="btn btn-ghost" onclick="app.closeModal()">取消</button>
                <button class="btn btn-primary" onclick="app.confirmPublish()">发布</button>
            </div>
        `;
    }

    getAddSourceModal() {
        return `
            <div class="modal-header">
                <h3 class="modal-title">添加自定义信息源</h3>
                <button class="modal-close" onclick="app.closeModal()">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"/>
                    </svg>
                </button>
            </div>
            <div class="form-group">
                <label class="form-label">信息源名称</label>
                <input type="text" class="form-input" placeholder="例如：TechCrunch">
            </div>
            <div class="form-group">
                <label class="form-label">源类型</label>
                <select class="form-input">
                    <option>RSS Feed</option>
                    <option>网站URL</option>
                    <option>社交媒体账号</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">URL地址</label>
                <input type="url" class="form-input" placeholder="https://example.com/feed">
            </div>
            <div class="form-group">
                <label class="form-label">内容类型</label>
                <div class="flex gap-sm" style="flex-wrap: wrap;">
                    <label class="checkbox-label">
                        <input type="checkbox"> 科技资讯
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox"> 生活方式
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox"> 时尚美妆
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox"> 美食旅行
                    </label>
                </div>
            </div>
            <div class="flex gap-md">
                <button class="btn btn-ghost" onclick="app.closeModal()">取消</button>
                <button class="btn btn-primary" onclick="app.confirmAddSource()">添加</button>
            </div>
        `;
    }

    createPersona() {
        this.showToast('新身份创建成功！', 'success');
        this.closeModal();
    }

    confirmAddSource() {
        this.showToast('自定义信息源添加成功！', 'success');
        this.closeModal();
    }

    confirmPublish() {
        this.showToast('内容发布成功！', 'success');
        this.closeModal();
        this.loadPage('dashboard');
    }

    // Additional helper methods for content generation
    getMediaGridWithTags() {
        const mediaItems = [
            { type: 'image', name: '春日野餐.jpg', tags: ['户外', '野餐', '春天'], used: false, category: '生活方式' },
            { type: 'image', name: '咖啡拍摄.jpg', tags: ['美食', '咖啡', '暖色调'], used: true, category: '美食' },
            { type: 'image', name: '穿搭展示.jpg', tags: ['时尚', '穿搭', '简约'], used: false, category: '时尚' },
            { type: 'image', name: '美食摄影.jpg', tags: ['美食', '摆盘', '精致'], used: true, category: '美食' },
            { type: 'image', name: '日常生活.jpg', tags: ['日常', '生活', '温馨'], used: false, category: '生活方式' },
            { type: 'image', name: '旅行风景.jpg', tags: ['旅行', '风景', '自然'], used: false, category: '生活方式' },
            { type: 'image', name: '居家布置.jpg', tags: ['居家', '装饰', '简约'], used: true, category: '生活方式' },
            { type: 'image', name: '时尚单品.jpg', tags: ['时尚', '单品', '配饰'], used: false, category: '时尚' }
        ];
        
        return mediaItems.map(item => `
            <div class="media-card ${item.used ? 'used' : 'unused'}" onclick="app.selectPhoto('${item.name}')">
                <div class="media-thumbnail">
                    <div class="media-icon">🖼️</div>
                    <div class="media-overlay">
                        <div class="media-status">${item.used ? '已使用' : '未使用'}</div>
                    </div>
                </div>
                <div class="media-info">
                    <div class="media-name">${item.name}</div>
                    <div class="media-tags">
                        ${item.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                    <div class="media-category">${item.category}</div>
                </div>
            </div>
        `).join('');
    }

    getMediaGrid() {
        const mediaItems = [
            { type: 'image', name: '春日樱花.jpg' },
            { type: 'image', name: '咖啡拍摄.jpg' },
            { type: 'video', name: '穿搭展示.mp4' },
            { type: 'image', name: '美食摄影.jpg' },
            { type: 'image', name: '日常生活.jpg' },
            { type: 'video', name: '旅行记录.mp4' },
            { type: 'image', name: '居家布置.jpg' },
            { type: 'image', name: '读书笔记.jpg' }
        ];
        
        return mediaItems.map(item => `
            <div class="media-item" onclick="app.selectMedia('${item.name}')">
                <div class="media-thumbnail">
                    <div class="media-icon">
                        ${item.type === 'image' ? '🖼️' : '🎥'}
                    </div>
                </div>
                <div class="media-name">${item.name}</div>
            </div>
        `).join('');
    }



    // Additional interactive methods
    setupEditor() {
        // Set up real-time preview
        const titleInput = document.getElementById('contentTitle');
        const bodyInput = document.getElementById('contentBody');
        const previewContent = document.getElementById('previewContent');
        
        if (titleInput && bodyInput && previewContent) {
            const updatePreview = () => {
                const title = titleInput.value || '输入标题...';
                const body = bodyInput.value || '开始创作你的内容...';
                previewContent.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 8px;">${title}</div>
                    <div style="white-space: pre-wrap;">${body}</div>
                `;
            };
            
            titleInput.addEventListener('input', updatePreview);
            bodyInput.addEventListener('input', updatePreview);
            updatePreview();
        }
    }

    // Additional feature implementations (removed editor-related methods)

    refreshTrends() {
        this.showToast('正在刷新趋势数据...', 'info');
        setTimeout(() => {
            this.showToast('趋势数据已更新！', 'success');
        }, 1500);
    }

    uploadMedia() {
        this.showToast('媒体上传功能开发中...', 'info');
    }

    selectMedia(mediaName) {
        this.showToast(`已选择: ${mediaName}`, 'success');
    }

    viewContent(title) {
        this.showToast(`查看内容: ${title}`, 'info');
        // Could show a modal with full content details
    }

    regenerateContent(title) {
        this.showToast(`正在重新生成: ${title}`, 'info');
        setTimeout(() => {
            this.showToast('内容已重新生成！', 'success');
            this.loadPage('content');
        }, 2000);
    }

    publishDraft(title) {
        this.showToast(`正在发布: ${title}`, 'info');
        setTimeout(() => {
            this.showToast('内容已成功发布！', 'success');
            this.loadPage('content');
        }, 1500);
    }

    editContent(title) {
        this.showToast(`查看内容: ${title}`, 'info');
        // Since we removed editor, just view the content
        this.viewContent(title);
    }


    // Photo-driven features
    analyzePhotos() {
        this.showToast('正在AI分析照片标签...', 'info');
        setTimeout(() => {
            this.showToast('照片分析完成！已识别场景、风格、色调', 'success');
        }, 3000);
    }

    selectPhoto(photoName) {
        this.showToast(`已选择照片: ${photoName}`, 'success');
        // Could navigate to editor and auto-select this photo
    }

    // Sources management
    addCustomSource() {
        this.showModal('addSource');
    }

    removeSource(sourceId) {
        this.showToast('信息源已移除', 'success');
    }

    // Enhanced persona features
    updatePersonaMemory() {
        this.showToast('人设记忆已更新', 'success');
    }

    // Toggle generation steps visibility
    toggleGenerationSteps() {
        const steps = document.getElementById('generationSteps');
        const button = document.getElementById('toggleSteps');
        
        if (steps.style.display === 'none') {
            steps.style.display = 'flex';
            button.innerHTML = `
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"/>
                </svg>
                收起
            `;
        } else {
            steps.style.display = 'none';
            button.innerHTML = `
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
                </svg>
                展开
            `;
        }
    }

    // Settings management
    saveSettings() {
        const userName = document.getElementById('userName')?.value || '';
        const userGender = document.getElementById('userGender')?.value || '';
        const userMBTI = document.getElementById('userMBTI')?.value || '';
        const userEmoji = document.getElementById('userEmoji')?.value || '';
        const userPersonality = document.getElementById('userPersonality')?.value || '';
        const userIntroduction = document.getElementById('userIntroduction')?.value || '';
        const userTopics = document.getElementById('userTopics')?.value || '';
        
        // In a real app, this would save to localStorage or send to server
        this.showToast('个人设置已保存！', 'success');
    }

    resetSettings() {
        // Reset all form fields to default values
        const defaults = {
            userName: '小红书创作者',
            userGender: '女',
            userMBTI: 'ENFP',
            userEmoji: '🌸',
            userPersonality: '活泼开朗、充满好奇心、热爱生活',
            userIntroduction: '热爱分享生活中的美好瞬间，喜欢记录日常点滴，用文字和照片传递正能量。',
            userTopics: '日常生活,美食,旅行,心情分享',
            writingStyle: 'warm',
            contentLength: 'medium'
        };
        
        Object.keys(defaults).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.value = defaults[key];
            }
        });
        
        this.showToast('设置已重置为默认值', 'success');
    }

    // Data management functions
    exportData() {
        this.showToast('正在导出数据...', 'info');
        setTimeout(() => {
            this.showToast('数据导出完成！', 'success');
        }, 2000);
    }

    clearCache() {
        this.showToast('正在清空缓存...', 'info');
        setTimeout(() => {
            this.showToast('缓存已清空', 'success');
        }, 1000);
    }

    backupData() {
        this.showToast('正在备份数据...', 'info');
        setTimeout(() => {
            this.showToast('数据备份完成！', 'success');
        }, 2000);
    }

    // Account management functions
    changePassword() {
        this.showToast('修改密码功能即将开放', 'info');
    }

    deleteAccount() {
        if (confirm('确定要删除账号吗？此操作不可撤销！')) {
            this.showToast('账号删除请求已提交', 'warning');
        }
    }

    logout() {
        if (confirm('确定要退出登录吗？')) {
            this.showToast('正在退出登录...', 'info');
            setTimeout(() => {
                this.showToast('已退出登录', 'success');
                // In a real app, this would redirect to login page
            }, 1000);
        }
    }

    // Knowledge base file upload
    getUploadedFiles() {
        const uploadedFiles = [
            {
                name: '创作指南.pdf',
                size: '2.3 MB',
                type: 'PDF',
                uploadTime: '2小时前',
                status: 'processed'
            },
            {
                name: '写作技巧汇总.docx',
                size: '1.8 MB',
                type: 'Word',
                uploadTime: '1天前',
                status: 'processed'
            },
            {
                name: '行业术语库.txt',
                size: '156 KB',
                type: 'TXT',
                uploadTime: '3天前',
                status: 'processed'
            },
            {
                name: '用户反馈数据.json',
                size: '892 KB',
                type: 'JSON',
                uploadTime: '1周前',
                status: 'processing'
            }
        ];

        return uploadedFiles.map(file => `
            <div class="uploaded-file-item">
                <div class="file-icon">
                    ${this.getFileIcon(file.type)}
                </div>
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-meta">
                        <span class="file-size">${file.size}</span>
                        <span class="file-separator">•</span>
                        <span class="file-time">${file.uploadTime}</span>
                        <span class="file-separator">•</span>
                        <span class="file-status ${file.status}">
                            ${file.status === 'processed' ? '✅ 已处理' : '⏳ 处理中'}
                        </span>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="btn btn-ghost btn-sm" onclick="app.downloadFile('${file.name}')">
                        <svg width="14" height="14" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"/>
                        </svg>
                    </button>
                    <button class="btn btn-ghost btn-sm" onclick="app.removeFile('${file.name}')">
                        <svg width="14" height="14" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm5 2a1 1 0 00-1 1v3a1 1 0 102 0V8a1 1 0 00-1-1zm3 1a1 1 0 10-2 0v3a1 1 0 102 0V8z"/>
                        </svg>
                    </button>
                </div>
            </div>
        `).join('');
    }

    getFileIcon(type) {
        const iconMap = {
            'PDF': '📄',
            'Word': '📝',
            'TXT': '📃',
            'Markdown': '📋',
            'JSON': '🗂️'
        };
        return iconMap[type] || '📎';
    }

    handleFileUpload(event) {
        const files = event.target.files;
        if (files.length === 0) return;

        Array.from(files).forEach(file => {
            const fileName = file.name;
            const fileSize = (file.size / 1024 / 1024).toFixed(2) + ' MB';
            
            this.showToast(`正在上传 ${fileName}...`, 'info');
            
            // Simulate file upload process
            setTimeout(() => {
                this.showToast(`${fileName} 上传完成，正在处理...`, 'success');
                
                // Simulate processing
                setTimeout(() => {
                    this.showToast(`${fileName} 处理完成，已加入知识库`, 'success');
                    // Refresh the uploaded files list
                    this.loadPage('sources');
                }, 2000);
            }, 1000);
        });
    }

    downloadFile(fileName) {
        this.showToast(`正在下载 ${fileName}...`, 'info');
        setTimeout(() => {
            this.showToast(`${fileName} 下载完成`, 'success');
        }, 1000);
    }

    removeFile(fileName) {
        if (confirm(`确定要删除 ${fileName} 吗？`)) {
            this.showToast(`正在删除 ${fileName}...`, 'info');
            setTimeout(() => {
                this.showToast(`${fileName} 已删除`, 'success');
                this.loadPage('sources');
            }, 1000);
        }
    }

    // Drag and Drop functionality
    setupDragAndDrop() {
        // This will be called when the sources page is loaded
        setTimeout(() => {
            const uploadArea = document.getElementById('uploadArea');
            if (uploadArea) {
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    uploadArea.addEventListener(eventName, this.preventDefaults, false);
                });

                ['dragenter', 'dragover'].forEach(eventName => {
                    uploadArea.addEventListener(eventName, () => {
                        uploadArea.classList.add('dragover');
                    }, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    uploadArea.addEventListener(eventName, () => {
                        uploadArea.classList.remove('dragover');
                    }, false);
                });

                uploadArea.addEventListener('drop', (e) => {
                    const files = e.dataTransfer.files;
                    this.handleFileUpload({ target: { files } });
                }, false);
            }
        }, 100);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // Generate from dashboard
    generateFromDashboard() {
        const topic = document.getElementById('quickTopicInput')?.value || '';
        const count = document.getElementById('generateCount')?.value || '1';
        
        if (topic) {
            this.showToast(`正在生成${count}篇关于"${topic}"的内容...`, 'info');
        } else {
            this.showToast(`正在AI智能生成${count}篇内容...`, 'info');
        }
        
        // Simulate AI generation process
        setTimeout(() => {
            this.showToast('正在选择合适的未使用照片...', 'info');
        }, 1000);
        setTimeout(() => {
            this.showToast('正在分析热点趋势...', 'info');
        }, 2000);
        setTimeout(() => {
            this.showToast('正在基于人设生成内容...', 'info');
        }, 3000);
        setTimeout(() => {
            this.loadPage('content');
            this.showToast(`${count}篇内容生成完成！已保存为草稿`, 'success');
        }, 4000);
    }

    // Select idea from dashboard
    selectIdea(ideaTitle) {
        const input = document.getElementById('quickTopicInput');
        if (input) {
            input.value = ideaTitle;
        }
        this.showToast(`已选择创作主题: ${ideaTitle}`, 'success');
    }

    // Select creation idea from content page
    selectCreationIdea(ideaTitle) {
        const input = document.getElementById('topicInput');
        if (input) {
            input.value = ideaTitle;
        }
        this.showToast(`已选择创作主题: ${ideaTitle}`, 'success');
        // Auto-scroll to the generate button
        const generateBtn = document.querySelector('.generate-btn-compact');
        if (generateBtn) {
            generateBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new XiaohongshuApp();
});

// Add CSS for additional components
const additionalStyles = `
.topic-card {
    padding: 16px;
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.topic-card:hover {
    background: var(--surface-lighter);
    border-color: var(--primary);
}

.trending-item {
    padding: 12px 0;
    border-bottom: 1px solid var(--border);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.trending-item:hover {
    background: var(--surface-light);
    padding-left: 8px;
    padding-right: 8px;
    border-radius: var(--radius-md);
}

.trending-item:last-child {
    border-bottom: none;
}

.suggestion-card {
    padding: 16px;
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.suggestion-card:hover {
    background: var(--surface-lighter);
    border-color: var(--primary);
}

.media-item {
    aspect-ratio: 1;
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
}

.media-item:hover {
    background: var(--surface-lighter);
    border-color: var(--primary);
}

.media-thumbnail {
    width: 60px;
    height: 60px;
    background: var(--surface);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.media-icon {
    font-size: 24px;
}

.media-name {
    font-size: 12px;
    text-align: center;
    color: var(--text-secondary);
}

.history-item {
    padding: 16px;
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.history-item:hover {
    background: var(--surface-lighter);
    border-color: var(--primary);
}

.performance-item {
    padding: 16px 0;
    border-bottom: 1px solid var(--border);
}

.performance-item:last-child {
    border-bottom: none;
}

.badge {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
}

.badge-primary { background: rgba(255, 46, 77, 0.1); color: var(--primary); }
.badge-secondary { background: var(--surface-lighter); color: var(--text-secondary); }
.badge-success { background: rgba(34, 197, 94, 0.1); color: var(--success); }
.badge-warning { background: rgba(245, 158, 11, 0.1); color: var(--warning); }
.badge-hot { background: rgba(255, 46, 77, 0.1); color: var(--primary); }
.badge-warm { background: rgba(245, 158, 11, 0.1); color: var(--warning); }
.badge-cool { background: rgba(59, 130, 246, 0.1); color: var(--info); }

.setting-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--surface-lighter);
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--surface-light);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.checkbox-label:hover {
    background: var(--surface-lighter);
    border-color: var(--primary);
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}
`;

// Inject additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);